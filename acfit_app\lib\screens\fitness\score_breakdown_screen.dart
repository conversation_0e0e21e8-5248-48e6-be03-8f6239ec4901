import 'package:flutter/material.dart';
// import 'dart:developer' as developer; // Import developer for logging (removed)
import '../../services/api_service.dart';
import '../../models/user_score.dart';
// import '../../models/score_history.dart';

class ScoreBreakdownScreen extends StatefulWidget {
  const ScoreBreakdownScreen({Key? key}) : super(key: key);

  @override
  State<ScoreBreakdownScreen> createState() => _ScoreBreakdownScreenState();
}

class _ScoreBreakdownScreenState extends State<ScoreBreakdownScreen> {
  final ApiService _apiService = ApiService();
  bool _isLoading = true;
  Map<String, dynamic> _scoreBreakdown = {};
  UserScore? _userScore;
  List<Map<String, dynamic>> _scoreHistory = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // Fetch score breakdown
      final scoreBreakdown = await _apiService.getUserScoreBreakdown();

      // Fetch user score
      final userScore = await _apiService.getUserScore();

      // Fetch score history
      final scoreHistoryData = await _apiService.getUserScoreHistory();
      // Process the score history data
      List<Map<String, dynamic>> history = [];
      if (scoreHistoryData.containsKey('history') &&
          scoreHistoryData['history'] is List) {
        final historyList = scoreHistoryData['history'] as List;
        history = List<Map<String, dynamic>>.from(
            historyList.map((item) => item as Map<String, dynamic>));
      }

      if (mounted) {
        setState(() {
          _scoreBreakdown = scoreBreakdown;
          _userScore = userScore;
          _scoreHistory = history;
          _isLoading = false;
        });
      }
    } catch (e) {
      // Removed log
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load score data: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Score Breakdown',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Work Sans',
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: const Color(0xFFFF7F36),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFF97316)))
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTotalScoreCard(),
                      const SizedBox(height: 24),
                      _buildScoreBreakdownSection(),
                      const SizedBox(height: 24),
                      _buildScoreHistorySection(),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildTotalScoreCard() {
    final totalScore = _userScore?.totalScore ?? 0;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFFFF7F36),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color:
                Colors.black.withValues(red: 0, green: 0, blue: 0, alpha: 25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Text(
            'Your Health Score',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'Work Sans',
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                '$totalScore',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 64,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Work Sans',
                ),
              ),
              const Text(
                '%',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Work Sans',
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _getScoreDescription(totalScore),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: 'Work Sans',
            ),
          ),
        ],
      ),
    );
  }

  String _getScoreDescription(int score) {
    if (score >= 90) return 'Excellent! Keep up the great work!';
    if (score >= 80) return 'Very good! You\'re doing well!';
    if (score >= 70) return 'Good! On the right track!';
    if (score >= 60) return 'Fair. Room for improvement.';
    if (score >= 50) return 'Average. Let\'s work on this!';
    return 'Let\'s improve your health habits!';
  }

  Widget _buildScoreBreakdownSection() {
    final breakdown = _scoreBreakdown['score_breakdown'];
    if (breakdown == null) {
      return const Center(
        child: Text(
          'Score breakdown not available',
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'Work Sans',
            color: Colors.grey,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Score Components',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            fontFamily: 'Work Sans',
            color: Color(0xFF101114),
          ),
        ),
        const SizedBox(height: 16),
        _buildScoreComponent('Workout Score', breakdown['workout_score']),
        _buildScoreComponent('Streak Score', breakdown['streak_score']),
        _buildScoreComponent('Nutrition Score', breakdown['nutrition_score']),
        _buildScoreComponent('Goal Score', breakdown['goal_score']),
      ],
    );
  }

  Widget _buildScoreComponent(String label, Map<String, dynamic>? data) {
    if (data == null) return const SizedBox.shrink();

    final value = data['value'] ?? 0;
    final weight = data['weight'] ?? '0%';
    final contribution = data['weighted_contribution'] ?? 0.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color:
                Colors.black.withValues(red: 0, green: 0, blue: 0, alpha: 13),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Work Sans',
                  color: Color(0xFF101114),
                ),
              ),
              Text(
                '$value',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Work Sans',
                  color: Color(0xFF101114),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: value / 100,
            backgroundColor: Colors.grey[200],
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFFF7F36)),
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Weight: $weight',
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Work Sans',
                  color: Colors.grey[600],
                ),
              ),
              Text(
                'Contribution: $contribution',
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Work Sans',
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScoreHistorySection() {
    if (_scoreHistory.isEmpty) {
      return const Center(
        child: Text(
          'Score history not available',
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'Work Sans',
            color: Colors.grey,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Score History',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            fontFamily: 'Work Sans',
            color: Color(0xFF101114),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black
                    .withValues(red: 0, green: 0, blue: 0, alpha: 13),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildScoreHistoryChart(),
        ),
      ],
    );
  }

  Widget _buildScoreHistoryChart() {
    // Simple placeholder for the chart
    // In a real implementation, you would use a chart library like fl_chart
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: _scoreHistory.take(7).map((entry) {
              final score = entry['score'] as int? ?? 0;
              final dateStr = entry['date'] as String? ?? '';
              final height = (score / 100) * 120; // Max height is 120

              return Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    width: 30,
                    height: height,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF7F36),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatDate(dateStr),
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'Work Sans',
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Last 7 days',
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Work Sans',
                color: Colors.grey[600],
              ),
            ),
            Text(
              'Average: ${_calculateAverageScore()}%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'Work Sans',
                color: Color(0xFF101114),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}';
    } catch (e) {
      return dateStr;
    }
  }

  int _calculateAverageScore() {
    if (_scoreHistory.isEmpty) return 0;

    final total = _scoreHistory.fold<int>(0, (sum, entry) {
      final scoreValue = entry['score'];
      final score = (scoreValue is int)
          ? scoreValue
          : (scoreValue is double)
              ? scoreValue.toInt()
              : 0;
      return sum + score;
    });

    return (total / _scoreHistory.length).round();
  }
}
