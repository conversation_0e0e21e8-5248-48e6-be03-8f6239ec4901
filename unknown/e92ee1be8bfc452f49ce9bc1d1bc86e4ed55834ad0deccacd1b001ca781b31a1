import 'package:json_annotation/json_annotation.dart';

part 'workout_video.g.dart';

@JsonSerializable()
class WorkoutVideo {
  final int id;
  final String title;
  final String? description;
  @J<PERSON><PERSON><PERSON>(name: 'video_url')
  final String? videoUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnail_url')
  final String? thumbnailUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'duration_seconds')
  final int? durationSeconds;
  final int order;
  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;

  WorkoutVideo({
    required this.id,
    required this.title,
    this.description,
    this.videoUrl,
    this.thumbnailUrl,
    this.durationSeconds,
    required this.order,
    this.createdAt,
  });

  factory WorkoutVideo.fromJson(Map<String, dynamic> json) =>
      _$WorkoutVideoFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutVideoToJson(this);

  /// Get formatted duration string (e.g., "5:30")
  String get formattedDuration {
    if (durationSeconds == null) return "Unknown";
    final minutes = durationSeconds! ~/ 60;
    final seconds = durationSeconds! % 60;
    return "$minutes:${seconds.toString().padLeft(2, '0')}";
  }
}
