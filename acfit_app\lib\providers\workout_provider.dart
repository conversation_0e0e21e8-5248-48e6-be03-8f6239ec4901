import 'package:flutter/material.dart';
import 'dart:async'; // Import for StreamSubscription
import '../services/api_service.dart';
import '../services/notification_service.dart'; // Import for notifications
import '../models/workout.dart'; // Imports ExerciseDetail, WorkoutDay etc.
import '../models/workout_log.dart'; // Imports TodaysWorkoutResponse, WorkoutLog
// Import for date formatting
import '../utils/logger.dart'; // Import for logging
import '../utils/date_utils.dart' as app_date_utils; // Use alias
import 'package:timezone/timezone.dart' as tz; // Import tz
import 'auth_provider.dart'; // Import AuthProvider to access user timezone

class WorkoutProvider extends ChangeNotifier {
  final ApiService apiService;
  final AuthProvider authProvider; // Add AuthProvider dependency
  final List<ExerciseDetail> _exercises = []; // Use ExerciseDetail
  Map<String, dynamic>? _currentWorkoutPlan;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _workoutPlan;
  TodaysWorkoutResponse? _todaysWorkout; // State for today's workout
  // Add state for storing workout data for different dates
  final Map<DateTime, TodaysWorkoutResponse?> _workoutData = {};

  // Subscriptions for notifications
  StreamSubscription? _cacheSubscription;
  StreamSubscription? _workoutCompletedSubscription;
  StreamSubscription? _workoutLogUpdatedSubscription;
  StreamSubscription? _dayChangedSubscription; // Add subscription variable

  WorkoutProvider({required this.apiService, required this.authProvider}) {
    // Listen for cache clearing notifications
    _cacheSubscription =
        NotificationService.instance.on('clear_workout_cache').listen((_) {
      // Clear workout cache notification received
      _clearCache();

      // Force a complete refresh of all workout data
      forceRefreshAllWorkoutData();
    });

    // Also listen for workout completion notifications
    _workoutCompletedSubscription =
        NotificationService.instance.on('workout_completed').listen((data) {
      // Workout completed notification received
      Logger.workout('Workout completed notification received with data: $data',
          tag: 'WorkoutProvider');

      // Extract workout log ID and date from notification data if available
      int? workoutLogId;
      String? dateStr;

      if (data is Map<String, dynamic>) {
        workoutLogId = data['workout_log_id'] as int?;
        dateStr = data['date'] as String?;
        Logger.workout(
            'Extracted from notification: workout_log_id=$workoutLogId, date=$dateStr',
            tag: 'WorkoutProvider');
      }

      // If we have both workout log ID and date, update the UI immediately
      if (workoutLogId != null &&
          workoutLogId > 0 &&
          dateStr != null &&
          dateStr.isNotEmpty) {
        // Parse the date string to DateTime
        DateTime date;
        try {
          date = DateTime.parse(dateStr);
          final dateKey = DateUtils.dateOnly(date);

          // Update the workout log in memory if we have it
          if (_workoutData.containsKey(dateKey) &&
              _workoutData[dateKey] != null) {
            var workoutDataForDate = _workoutData[dateKey]!;
            for (int i = 0; i < workoutDataForDate.workoutLogs.length; i++) {
              if (workoutDataForDate.workoutLogs[i].id == workoutLogId) {
                Logger.workout(
                    'Found workout log in memory, updating isCompleted to true',
                    tag: 'WorkoutProvider');
                workoutDataForDate.workoutLogs[i] =
                    workoutDataForDate.workoutLogs[i].copyWith(
                  isCompleted: true,
                  completionTime: DateTime.now(),
                );
                notifyListeners(); // Notify listeners immediately
                break;
              }
            }
          }
        } catch (e) {
          Logger.error('Error updating workout log from notification: $e',
              tag: 'WorkoutProvider');
        }
      }

      // Add a small delay to ensure the backend has processed the completion
      Future.delayed(const Duration(milliseconds: 500), () {
        forceRefreshAllWorkoutData();
      });
    });

    // Also listen for workout log update notifications
    _workoutLogUpdatedSubscription =
        NotificationService.instance.on('workout_log_updated').listen((data) {
      // Workout log updated notification received
      Logger.workout(
          'Workout log updated notification received with data: $data',
          tag: 'WorkoutProvider');

      // Extract workout log ID and date from notification data if available
      int? workoutLogId;
      String? dateStr;

      if (data is Map<String, dynamic>) {
        workoutLogId = data['workout_log_id'] as int?;
        dateStr = data['date'] as String?;
        Logger.workout(
            'Extracted from notification: workout_log_id=$workoutLogId, date=$dateStr',
            tag: 'WorkoutProvider');
      }

      // If we have both workout log ID and date, update the UI immediately
      if (workoutLogId != null &&
          workoutLogId > 0 &&
          dateStr != null &&
          dateStr.isNotEmpty) {
        // Parse the date string to DateTime
        DateTime date;
        try {
          date = DateTime.parse(dateStr);
          final dateKey = DateUtils.dateOnly(date);

          // Update the workout log in memory if we have it
          if (_workoutData.containsKey(dateKey) &&
              _workoutData[dateKey] != null) {
            var workoutDataForDate = _workoutData[dateKey]!;
            for (int i = 0; i < workoutDataForDate.workoutLogs.length; i++) {
              if (workoutDataForDate.workoutLogs[i].id == workoutLogId) {
                Logger.workout(
                    'Found workout log in memory, updating isCompleted to true',
                    tag: 'WorkoutProvider');
                workoutDataForDate.workoutLogs[i] =
                    workoutDataForDate.workoutLogs[i].copyWith(
                  isCompleted: true,
                  completionTime: DateTime.now(),
                );
                notifyListeners(); // Notify listeners immediately
                break;
              }
            }
          }
        } catch (e) {
          Logger.error('Error updating workout log from notification: $e',
              tag: 'WorkoutProvider');
        }
      }

      // Add a small delay to ensure the backend has processed the update
      Future.delayed(const Duration(milliseconds: 500), () {
        forceRefreshAllWorkoutData();
      });
    });

    // Listen for user day change notifications
    _dayChangedSubscription =
        NotificationService.instance.on('user_day_changed').listen((_) {
      Logger.workout(
          'User day changed notification received. Refreshing all workout data.',
          tag: 'WorkoutProvider');
      forceRefreshAllWorkoutData(); // Call the existing refresh method
    });
  }

  @override
  void dispose() {
    // Cancel all subscriptions when provider is disposed
    _cacheSubscription?.cancel();
    _workoutCompletedSubscription?.cancel();
    _workoutLogUpdatedSubscription?.cancel();
    _dayChangedSubscription?.cancel(); // Cancel day change subscription
    super.dispose();
  }

  // Clear the workout data cache
  void _clearCache() {
    // Clearing workout data cache
    _workoutData.clear();
    _todaysWorkout = null;
    notifyListeners();
  }

  // Helper to get current date in user's timezone
  tz.TZDateTime _getCurrentUserDate() {
    // Access timezone via authProvider.user.profile map
    final String? userTimezone =
        authProvider.user?.profile?['timezone'] as String?;
    final location = app_date_utils.getLocation(userTimezone);
    return app_date_utils.getCurrentDateInLocation(location);
  }

  // Force a complete refresh of all workout data
  Future<void> forceRefreshAllWorkoutData() async {
    Logger.workout('Starting complete workout data refresh',
        tag: 'WorkoutProvider');
    _clearCache();

    try {
      // Refresh today's workout (using timezone-aware date)
      final today = _getCurrentUserDate();
      Logger.workout('Loading workout for today: $today',
          tag: 'WorkoutProvider');
      await fetchWorkoutForDate(today, forceRefresh: true);

      // Notify listeners after today's data is fetched to update UI immediately
      notifyListeners();

      // Get location once for yesterday/tomorrow calculation
      final location = app_date_utils
          .getLocation(authProvider.user?.profile?['timezone'] as String?);
      final yesterday =
          tz.TZDateTime(location, today.year, today.month, today.day - 1);
      final tomorrow =
          tz.TZDateTime(location, today.year, today.month, today.day + 1);

      Logger.workout('Fetching workout for yesterday: $yesterday',
          tag: 'WorkoutProvider');
      await fetchWorkoutForDate(yesterday, forceRefresh: true);

      // Notify listeners after yesterday's data is fetched
      notifyListeners();

      Logger.workout('Fetching workout for tomorrow: $tomorrow',
          tag: 'WorkoutProvider');
      await fetchWorkoutForDate(tomorrow, forceRefresh: true);

      // Notify listeners after tomorrow's data is fetched
      notifyListeners();

      // Also refresh the current week's data
      final startOfWeek = tz.TZDateTime(
          location, today.year, today.month, today.day - today.weekday + 1);
      for (int i = 0; i < 7; i++) {
        final weekDate = tz.TZDateTime(
            location, startOfWeek.year, startOfWeek.month, startOfWeek.day + i);
        // Skip dates we've already fetched
        if (weekDate == yesterday ||
            weekDate == today ||
            weekDate == tomorrow) {
          continue;
        }
        Logger.workout('Fetching workout for week day: $weekDate',
            tag: 'WorkoutProvider');
        await fetchWorkoutForDate(weekDate, forceRefresh: true);

        // Notify listeners after each day's data is fetched
        notifyListeners();
      }

      // Force refresh calorie data
      try {
        await apiService.getCalories(skipCache: true);
        Logger.workout('Successfully refreshed calorie data',
            tag: 'WorkoutProvider');
      } catch (e) {
        Logger.error('Failed to refresh calorie data: $e',
            tag: 'WorkoutProvider');
      }

      // Also refresh today's workout data one more time to ensure it's up to date
      await fetchWorkoutForDate(today, forceRefresh: true);

      // Explicitly notify all listeners that workout data has been refreshed
      NotificationService.instance.notifyListeners('workout_data_refreshed');

      Logger.workout('All workout data refreshed successfully',
          tag: 'WorkoutProvider');
    } catch (e) {
      Logger.error('Failed to refresh workout data: $e',
          tag: 'WorkoutProvider');
    } finally {
      // Final notification to ensure all UI components are updated
      notifyListeners();
    }
  }

  // Getters
  List<ExerciseDetail> get exercises => _exercises; // Use ExerciseDetail
  Map<String, dynamic>? get currentWorkoutPlan => _currentWorkoutPlan;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic>? get workoutPlan => _workoutPlan;
  TodaysWorkoutResponse? get todaysWorkout => _todaysWorkout;
  // Getter for workout data for specific dates
  Map<DateTime, TodaysWorkoutResponse?> get workoutData => _workoutData;

  // Fetch workout data for a specific date and store it
  Future<void> fetchWorkoutForDate(DateTime date,
      {bool forceRefresh = false}) async {
    // Normalize the incoming date to the user's timezone date part
    final String? userTimezone =
        authProvider.user?.profile?['timezone'] as String?;
    final location = app_date_utils.getLocation(userTimezone);
    final dateKey = tz.TZDateTime(location, date.year, date.month, date.day);
    final dateStr =
        app_date_utils.formatDate(dateKey); // Format the timezone-aware date

    // Skip if already loading
    if (_isLoading) {
      return;
    }

    // Use cached data if available and not forcing refresh
    if (_workoutData.containsKey(dateKey) && !forceRefresh) {
      return;
    }

    _isLoading = true;
    _error = null;

    try {
      Logger.info('Fetching workout for date $dateStr', tag: 'WorkoutProvider');

      // Use date-based endpoint
      final response = await apiService.getWorkoutForDate(dateKey);

      Logger.info(
          'Fetched workout data for date $dateStr: Sessions count = ${response.sessions?.length}',
          tag: 'WorkoutProvider');
      if (response.sessions != null && response.sessions!.isNotEmpty) {
        Logger.info(
            'First session details: ${response.sessions!.first.toJson()}',
            tag: 'WorkoutProvider');
      }

      _workoutData[dateKey] = response;
    } catch (e) {
      _error = 'Failed to load workout for $dateStr: ${e.toString()}';
      _workoutData[dateKey] = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Note: Removed unused methods _loadExercises and _loadCurrentWorkoutPlan

  // Filter exercises by muscle group
  List<ExerciseDetail> getExercisesByMuscleGroup(String muscleGroup) {
    // Use ExerciseDetail and handle potential null muscleGroup
    return _exercises.where((e) => e.muscleGroup == muscleGroup).toList();
  }

  // Filter exercises by difficulty
  List<ExerciseDetail> getExercisesByDifficulty(String difficulty) {
    // Use ExerciseDetail and handle potential null difficultyLevel
    // Convert difficulty string to int for comparison if needed
    int? difficultyLevel = int.tryParse(difficulty);
    if (difficultyLevel == null) {
      return []; // Return empty if difficulty is not a number
    }
    return _exercises
        .where((e) => e.difficultyLevel == difficultyLevel)
        .toList();
  }

  // Filter exercises by equipment
  List<ExerciseDetail> getExercisesByEquipment(String equipment) {
    // Use ExerciseDetail and handle potential null equipmentRequired
    return _exercises.where((e) => e.equipmentRequired == equipment).toList();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<void> loadWorkoutPlan() async {
    _isLoading = true;
    _error = null;
    // notifyListeners(); // Moved to finally

    try {
      // Corrected method name was already applied in the previous step, ensuring it's correct here.
      final response = await apiService.getUserWorkoutPlans();

      if (response.isNotEmpty) {
        // Use the first workout plan in the list
        _workoutPlan = Map<String, dynamic>.from(response[0]);
      } else {
        // Provide fallback data when no workout plans are available
        _workoutPlan = {
          'id': 'fallback',
          'name': 'Default Workout Plan',
          'description':
              'Please check back later for personalized workout plans',
          'duration_weeks': 4,
          'workouts_per_week': 3
        };
      }

      // notifyListeners(); // Removed redundant call, finally block handles it
    } catch (e) {
      // Error loading workout plan

      // Provide fallback data in case of error
      _workoutPlan = {
        'id': 'fallback',
        'name': 'Default Workout Plan',
        'description': 'Unable to load workout plan. Please try again later.',
        'duration_weeks': 4,
        'workouts_per_week': 3
      };

      _error = 'Failed to load workout plans';
      // notifyListeners(); // Moved to finally
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify after loading finishes or fails
    }
  }

  Future<void> updateWorkoutPlan(Map<String, dynamic> plan) async {
    try {
      // Added data: label
      final response =
          await apiService.post('/workouts/workout-plans/', data: plan);
      _workoutPlan = response;
      // notifyListeners(); // Moved to finally
    } catch (e) {
      // Error updating workout plan
      _error = 'Failed to update workout plan: $e'; // Optionally set error
    } finally {
      // Add finally block if needed, e.g., to always notify or reset loading
      // _isLoading = false; // Assuming update is quick or handled elsewhere
      notifyListeners(); // Notify after attempt
    }
  }

  // Mark a workout as complete and update the state
  Future<void> markWorkoutComplete(int workoutLogId, String dateStr) async {
    if (workoutLogId <= 0) {
      Logger.error('Invalid workout log ID: $workoutLogId',
          tag: 'WorkoutProvider');
      return;
    }
    Logger.workout('Marking workout complete: ID=$workoutLogId, Date=$dateStr',
        tag: 'WorkoutProvider');

    // Parse the date string to DateTime
    DateTime date;
    try {
      date = DateTime.parse(dateStr);
    } catch (e) {
      // If parsing fails, use today's date
      date = DateTime.now();
      Logger.error('Failed to parse date: $dateStr, using today instead',
          tag: 'WorkoutProvider');
    }

    // Set loading state
    _isLoading = true;
    notifyListeners();

    try {
      // First, immediately update the UI to show completion
      final dateKey = DateUtils.dateOnly(date);
      if (_workoutData.containsKey(dateKey)) {
        var workoutDataForDate = _workoutData[dateKey];
        if (workoutDataForDate != null) {
          // Update the workout log in memory
          bool foundLog = false;
          for (int i = 0; i < workoutDataForDate.workoutLogs.length; i++) {
            if (workoutDataForDate.workoutLogs[i].id == workoutLogId) {
              Logger.workout(
                  'Found workout log in memory, updating isCompleted to true',
                  tag: 'WorkoutProvider');
              // Create a new copy with isCompleted set to true
              workoutDataForDate.workoutLogs[i] =
                  workoutDataForDate.workoutLogs[i].copyWith(
                isCompleted: true,
                completionTime: DateTime.now(),
              );
              foundLog = true;
              break;
            }
          }

          // If we didn't find the log, it might be a new log that wasn't in our cache
          if (!foundLog) {
            Logger.workout(
                'Workout log ID $workoutLogId not found in memory, will refresh from server',
                tag: 'WorkoutProvider');
          }
        }
      } else {
        Logger.workout(
            'No workout data found for date $dateStr in memory, will refresh from server',
            tag: 'WorkoutProvider');
      }

      // Notify listeners to update UI immediately
      notifyListeners();

      // Call the backend to mark the workout log as complete
      await apiService.completeWorkoutLog(workoutLogId: workoutLogId);
      Logger.workout('API call to complete workout log $workoutLogId sent.',
          tag: 'WorkoutProvider');

      // Add a small delay to ensure the backend has processed the completion
      await Future.delayed(const Duration(milliseconds: 500));

      // Force a complete refresh of all workout data from the server
      await forceRefreshAllWorkoutData();

      // Add another small delay and refresh again to ensure everything is updated
      await Future.delayed(const Duration(milliseconds: 500));
      await forceRefreshAllWorkoutData();

      // Update today's workout data specifically to ensure home screen badges are updated
      final today = DateTime.now();
      final todayKey = DateUtils.dateOnly(today);
      await fetchWorkoutForDate(todayKey, forceRefresh: true);

      Logger.workout('Successfully refreshed workout data after completion',
          tag: 'WorkoutProvider');
    } catch (e) {
      Logger.error('Failed to refresh workout data after completion: $e',
          tag: 'WorkoutProvider');

      // Even if there's an error, try to refresh the data one more time
      try {
        await Future.delayed(const Duration(milliseconds: 1000));
        await forceRefreshAllWorkoutData();

        // Try to fetch today's data specifically
        final today = DateTime.now();
        final todayKey = DateUtils.dateOnly(today);
        await fetchWorkoutForDate(todayKey, forceRefresh: true);
      } catch (e2) {
        Logger.error('Failed final attempt to refresh workout data: $e2',
            tag: 'WorkoutProvider');
      }
    } finally {
      // Reset loading state
      _isLoading = false;

      // Notify listeners again after all updates
      notifyListeners();
    }
  }
}
