from django.contrib import admin
from django.urls import path, reverse
from django.template.response import TemplateResponse
from django.shortcuts import redirect, render
from django.contrib import messages
from django.utils.safestring import mark_safe
from django.db import transaction
import logging

logger = logging.getLogger(__name__)
from multiselectfield import MultiSelectField

from .forms import BulkAssignPlanForm, QuestionnaireForm, QuestionnaireOptionForm
from .models import QuestionnaireQuestion, QuestionnaireOption
from .widgets import CustomMultiSelectWidget, CustomMultiSelectDisplayWidget

class ACFitAdminSite(admin.AdminSite):
    site_header = 'AC-FIT Admin'
    site_title = 'AC-FIT Admin Portal'
    index_title = 'Admin Dashboard'

    def has_permission(self, request):
        """Override to ensure superusers always have access without email verification"""
        # Check if user is authenticated and is a superuser
        if request.user.is_authenticated and request.user.is_superuser:
            # Ensure the user is marked as active
            if not request.user.is_active:
                request.user.is_active = True
                request.user.save()
            return True
        return False

    def index(self, request, extra_context=None):
        """Override the index view to use our custom dashboard"""
        return self.dashboard_view(request)

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('dashboard/', self.admin_view(self.dashboard_view), name='dashboard'),
            path('assign-plans/', self.admin_view(self.assign_plans_view), name='assign-plans'),
            path('questionnaire-manager/', self.admin_view(self.questionnaire_manager_view), name='questionnaire-manager'),
            path('products-dashboard/', self.admin_view(self.products_dashboard_view), name='products-dashboard'),
            path('questionnaire-manager/add-question/', self.admin_view(self.add_question_view), name='add-question'),
            path('questionnaire-manager/edit-question/<int:question_id>/', self.admin_view(self.edit_question_view), name='edit-question'),
            path('questionnaire-manager/delete-question/<int:question_id>/', self.admin_view(self.delete_question_view), name='delete-question'),
            path('questionnaire-manager/add-option/<int:question_id>/', self.admin_view(self.add_option_view), name='add-option'),
            path('questionnaire-manager/edit-option/<int:option_id>/', self.admin_view(self.edit_option_view), name='edit-option'),
            path('questionnaire-manager/delete-option/<int:option_id>/', self.admin_view(self.delete_option_view), name='delete-option'),
            # Add custom URLs for workout plans
            path('workout-plans/', self.admin_view(self.workout_plans_view), name='workout-plans'),
            path('workout-plans/add/', self.admin_view(self.add_workout_plan_view), name='add-workout-plan'),
            # Add custom URLs for user workout plans
            path('user-workout-plans/', self.admin_view(self.user_workout_plans_view), name='user-workout-plans'),
            path('user-workout-plans/<int:plan_id>/', self.admin_view(self.view_user_workout_plan), name='view-user-workout-plan'),
            path('user-workout-plans/<int:plan_id>/sync/', self.admin_view(self.sync_user_workout_plan), name='sync-user-workout-plan'),
            path('user-workout-plans/<int:plan_id>/edit/', self.admin_view(self.edit_user_workout_plan_view), name='edit-user-workout-plan'),
            path('user-workout-plans/add/', self.admin_view(self.add_user_workout_plan_view), name='add-user-workout-plan'),
            path('workout-plans/<int:plan_id>/edit/', self.admin_view(self.edit_workout_plan_view), name='edit-workout-plan'),
            # Workout day URLs
            path('workout-days/add/<int:plan_id>/', self.admin_view(self.add_workout_day_view), name='add-workout-day'),
            path('workout-days/<int:day_id>/edit/', self.admin_view(self.edit_workout_day_view), name='edit-workout-day'),
            path('workout-days/<int:day_id>/delete/', self.admin_view(self.delete_workout_day_view), name='delete-workout-day'),
            # Workout session URLs
            path('workout-sessions/add/<int:day_id>/', self.admin_view(self.add_workout_session_view), name='add-workout-session'),
            path('workout-sessions/<int:session_id>/edit/', self.admin_view(self.edit_workout_session_view), name='edit-workout-session'),
            path('workout-sessions/<int:session_id>/delete/', self.admin_view(self.delete_workout_session_view), name='delete-workout-session'),
            # Workout section URLs
            path('workout-sections/add/<int:session_id>/', self.admin_view(self.add_workout_section_view), name='add-workout-section'),
            path('workout-sections/<int:section_id>/edit/', self.admin_view(self.edit_workout_section_view), name='edit-workout-section'),
            path('workout-sections/<int:section_id>/delete/', self.admin_view(self.delete_workout_section_view), name='delete-workout-section'),
            # Exercise instance URLs
            path('exercise-instances/add/<int:section_id>/', self.admin_view(self.add_exercise_instance_view), name='add-exercise-instance'),
            path('exercise-instances/<int:instance_id>/edit/', self.admin_view(self.edit_exercise_instance_view), name='edit-exercise-instance'),
            path('exercise-instances/<int:instance_id>/delete/', self.admin_view(self.delete_exercise_instance_view), name='delete-exercise-instance'),
            # Workout schedule URLs
            path('workout-schedules/add/<int:plan_id>/', self.admin_view(self.add_workout_schedule_view), name='add-workout-schedule'),
            path('workout-schedules/<int:schedule_id>/edit/', self.admin_view(self.edit_workout_schedule_view), name='edit-workout-schedule'),
            path('workout-schedules/<int:schedule_id>/delete/', self.admin_view(self.delete_workout_schedule_view), name='delete-workout-schedule'),
            # Add custom URLs for meal plans
            path('meal-plans/', self.admin_view(self.meal_plans_view), name='meal-plans'),
            path('meal-plans/add/', self.admin_view(self.add_meal_plan_view), name='add-meal-plan'),
            path('meal-plans/<int:plan_id>/edit/', self.admin_view(self.edit_meal_plan_view), name='edit-meal-plan'),
            # Add custom URLs for user meal plans
            path('user-meal-plans/', self.admin_view(self.user_meal_plans_view), name='user-meal-plans'),
            path('user-meal-plans/<int:plan_id>/', self.admin_view(self.view_user_meal_plan), name='view-user-meal-plan'),
            path('user-meal-plans/<int:plan_id>/sync/', self.admin_view(self.sync_user_meal_plan), name='sync-user-meal-plan'),
            path('user-meal-plans/<int:plan_id>/edit/', self.admin_view(self.edit_user_meal_plan_view), name='edit-user-meal-plan'),
            path('user-meal-plans/add/', self.admin_view(self.add_user_meal_plan_view), name='add-user-meal-plan'),
            # Daily meal plan URLs
            path('daily-meal-plans/add/<int:plan_id>/', self.admin_view(self.add_daily_meal_plan_view), name='add-daily-meal-plan'),
            path('daily-meal-plans/<int:day_id>/edit/', self.admin_view(self.edit_daily_meal_plan_view), name='edit-daily-meal-plan'),
            path('daily-meal-plans/<int:day_id>/delete/', self.admin_view(self.delete_daily_meal_plan_view), name='delete-daily-meal-plan'),
            # Meal schedule URLs
            path('meal-schedules/add/<int:plan_id>/', self.admin_view(self.add_meal_schedule_view), name='add-meal-schedule'),
            path('meal-schedules/<int:schedule_id>/edit/', self.admin_view(self.edit_meal_schedule_view), name='edit-meal-schedule'),
            path('meal-schedules/<int:schedule_id>/delete/', self.admin_view(self.delete_meal_schedule_view), name='delete-meal-schedule'),
            # Add custom URLs for users
            path('users/', self.admin_view(self.users_view), name='users'),
            path('users/add/', self.admin_view(self.add_user_view), name='add-user'),
            path('users/<uuid:user_id>/edit/', self.admin_view(self.edit_user_view), name='edit-user'),
            path('users/<uuid:user_id>/delete/', self.admin_view(self.delete_user_view), name='delete-user'),

            # Add custom URLs for FAQs
            path('faqs/', self.admin_view(self.faqs_view), name='faqs'),
            path('faqs/add/', self.admin_view(self.add_faq_view), name='add-faq'),
            path('faqs/<int:faq_id>/edit/', self.admin_view(self.edit_faq_view), name='edit-faq'),
            path('faqs/<int:faq_id>/delete/', self.admin_view(self.delete_faq_view), name='delete-faq'),

            # Add custom URLs for feedback
            path('feedback/', self.admin_view(self.feedback_view), name='feedback'),
            path('feedback/<int:feedback_id>/', self.admin_view(self.view_feedback), name='view-feedback'),
            path('feedback/<int:feedback_id>/resolve/', self.admin_view(self.resolve_feedback), name='resolve-feedback'),

            # Calendar views
            path('workout-calendar/', self.admin_view(self.workout_calendar_view), name='workout-calendar'),
            path('meal-calendar/', self.admin_view(self.meal_calendar_view), name='meal-calendar'),
            path('program-days/', self.admin_view(self.program_day_view), name='program-days'),

            # Documentation view
            path('documentation/', self.admin_view(self.documentation_view), name='documentation'),
        ]
        return custom_urls + urls

    def dashboard_view(self, request):
        """Custom dashboard view with key metrics and quick actions"""
        # Import here to avoid circular imports
        from accounts.models import UserProfile
        from workouts.models import WorkoutPlan, UserWorkoutPlan
        from meals.models import MealPlan, UserMealPlan
        from support.models import UserFeedback

        # Get counts for key models
        user_count = UserProfile.objects.count()
        workout_plans = WorkoutPlan.objects.all()
        meal_plans = MealPlan.objects.all()
        active_workout_plans = UserWorkoutPlan.objects.filter(is_active=True).count()
        active_meal_plans = UserMealPlan.objects.filter(is_active=True).count()

        # Get activity log count
        from activity_logs.models import ActivityLog
        activity_log_count = ActivityLog.objects.count()

        # Get recent users
        recent_users = UserProfile.objects.all().order_by('-user__date_joined')[:5]

        # Get recent feedback
        recent_feedback = UserFeedback.objects.select_related('user').order_by('-created_at')[:5]

        # Get recent activity logs
        from activity_logs.models import ActivityLog
        recent_activity_logs = ActivityLog.objects.select_related('user__user').order_by('-timestamp')[:5]

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'user_count': user_count,
            'workout_plan_count': workout_plans.count(),
            'meal_plan_count': meal_plans.count(),
            'active_workout_plans': active_workout_plans,
            'active_meal_plans': active_meal_plans,
            'activity_log_count': activity_log_count,
            'recent_users': recent_users,
            'recent_feedback': recent_feedback,
            'recent_workout_plans': workout_plans.order_by('-created_at')[:5],
            'recent_meal_plans': meal_plans.order_by('-created_at')[:5],
            'recent_activity_logs': recent_activity_logs,
            'app_list': app_list,
            'title': 'Dashboard',
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/dashboard.html', context)

    def assign_plans_view(self, request):
        """View for assigning workout and meal plans to users"""
        # Import here to avoid circular imports
        from accounts.models import UserProfile
        from workouts.models import WorkoutPlan, UserWorkoutPlan
        from meals.models import MealPlan, UserMealPlan
        from django.contrib.auth import get_user_model

        User = get_user_model()

        if request.method == 'POST':
            form = BulkAssignPlanForm(request.POST)
            if form.is_valid():
                users = form.cleaned_data['users']
                workout_plan = form.cleaned_data['workout_plan']
                meal_plan = form.cleaned_data['meal_plan']
                start_date = form.cleaned_data['start_date']
                replace_existing = form.cleaned_data['replace_existing']

                # Process the form data
                with transaction.atomic():
                    for user in users:
                        profile = UserProfile.objects.get(user=user)

                        # Assign workout plan if selected
                        if workout_plan:
                            if replace_existing:
                                # Deactivate existing active plans
                                UserWorkoutPlan.objects.filter(user=profile, is_active=True).update(is_active=False)

                            # Create new user workout plan
                            UserWorkoutPlan.objects.create(
                                user=profile,
                                workout_plan=workout_plan,
                                start_date=start_date,
                                is_active=True
                            )

                        # Assign meal plan if selected
                        if meal_plan:
                            if replace_existing:
                                # Deactivate existing active plans
                                UserMealPlan.objects.filter(user=profile, is_active=True).update(is_active=False)

                            # Create new user meal plan
                            UserMealPlan.objects.create(
                                user=profile,
                                meal_plan=meal_plan,
                                start_date=start_date,
                                is_active=True
                            )

                messages.success(request, f'Successfully assigned plans to {len(users)} users.')
                return redirect('acfit_admin:dashboard')
        else:
            form = BulkAssignPlanForm()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Assign Plans to Users',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/assign_plans.html', context)



    def questionnaire_manager_view(self, request):
        """View for managing questionnaire questions and options"""
        questions = QuestionnaireQuestion.objects.all().order_by('order')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'questions': questions,
            'title': 'Questionnaire Manager',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/questionnaire_manager.html', context)

    def products_dashboard_view(self, request):
        """View for managing products"""
        from products.models import Product

        # Get search query if any
        search_query = request.GET.get('search', '')

        # Filter products based on search query
        if search_query:
            products = Product.objects.filter(name__icontains=search_query) | Product.objects.filter(description__icontains=search_query)
        else:
            products = Product.objects.all()

        # Order products by creation date (newest first)
        products = products.order_by('-created_at')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'products': products,
            'search_query': search_query,
            'title': 'Products Dashboard',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/products_dashboard.html', context)

    def add_question_view(self, request):
        """View for adding a new questionnaire question"""
        if request.method == 'POST':
            form = QuestionnaireForm(request.POST)
            if form.is_valid():
                form.save()
                messages.success(request, 'Question added successfully.')
                return redirect('acfit_admin:questionnaire-manager')
        else:
            form = QuestionnaireForm()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Add Question',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/questionnaire_form.html', context)

    def edit_question_view(self, request, question_id):
        """View for editing an existing questionnaire question"""
        question = QuestionnaireQuestion.objects.get(id=question_id)

        if request.method == 'POST':
            form = QuestionnaireForm(request.POST, instance=question)
            if form.is_valid():
                form.save()
                messages.success(request, 'Question updated successfully.')
                return redirect('acfit_admin:questionnaire-manager')
        else:
            form = QuestionnaireForm(instance=question)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'question': question,
            'title': 'Edit Question',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/questionnaire_form.html', context)

    def delete_question_view(self, request, question_id):
        """View for deleting a questionnaire question"""
        question = QuestionnaireQuestion.objects.get(id=question_id)

        if request.method == 'POST':
            question.delete()
            messages.success(request, 'Question deleted successfully.')
            return redirect('acfit_admin:questionnaire-manager')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'question': question,
            'title': 'Delete Question',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/questionnaire_delete.html', context)

    def add_option_view(self, request, question_id):
        """View for adding a new option to a question"""
        question = QuestionnaireQuestion.objects.get(id=question_id)

        if request.method == 'POST':
            form = QuestionnaireOptionForm(request.POST)
            if form.is_valid():
                option = form.save(commit=False)
                option.question = question
                option.save()
                messages.success(request, 'Option added successfully.')
                return redirect('acfit_admin:questionnaire-manager')
        else:
            form = QuestionnaireOptionForm(initial={'question': question})

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'question': question,
            'title': f'Add Option to "{question.text}"',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/questionnaire_option_form.html', context)

    def edit_option_view(self, request, option_id):
        """View for editing an existing option"""
        option = QuestionnaireOption.objects.get(id=option_id)

        if request.method == 'POST':
            form = QuestionnaireOptionForm(request.POST, instance=option)
            if form.is_valid():
                form.save()
                messages.success(request, 'Option updated successfully.')
                return redirect('acfit_admin:questionnaire-manager')
        else:
            form = QuestionnaireOptionForm(instance=option)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'option': option,
            'title': 'Edit Option',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/questionnaire_option_form.html', context)

    def delete_option_view(self, request, option_id):
        """View for deleting an option"""
        option = QuestionnaireOption.objects.get(id=option_id)

        if request.method == 'POST':
            option.delete()
            messages.success(request, 'Option deleted successfully.')
            return redirect('acfit_admin:questionnaire-manager')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'option': option,
            'title': 'Delete Option',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/questionnaire_option_delete.html', context)

    # Workout Plan Views
    def workout_plans_view(self, request):
        """View for listing all workout plans"""
        from workouts.models import WorkoutPlan

        workout_plans = WorkoutPlan.objects.all().order_by('-created_at')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'workout_plans': workout_plans,
            'title': 'Workout Plans',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_plans.html', context)

    def user_workout_plans_view(self, request):
        """View for listing all user workout plans"""
        from workouts.models import UserWorkoutPlan, WorkoutPlan
        from django.contrib.auth import get_user_model
        from django.core.paginator import Paginator
        from datetime import date

        User = get_user_model()

        # Get filter parameters
        user_id = request.GET.get('user')
        workout_plan_id = request.GET.get('workout_plan')
        is_active = request.GET.get('is_active')
        is_modified = request.GET.get('is_modified')

        # Base queryset
        queryset = UserWorkoutPlan.objects.select_related('user__user', 'workout_plan').order_by('-start_date')

        # Apply filters
        if user_id:
            queryset = queryset.filter(user__user_id=user_id)
        if workout_plan_id:
            queryset = queryset.filter(workout_plan_id=workout_plan_id)
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)
        if is_modified == 'true':
            queryset = queryset.filter(is_modified=True)
        elif is_modified == 'false':
            queryset = queryset.filter(is_modified=False)

        # Add current week and day information
        today = date.today()
        for plan in queryset:
            if plan.start_date:
                days_since_start = (today - plan.start_date).days
                if days_since_start >= 0:
                    # Calculate current week (1-indexed)
                    plan.current_week = (days_since_start // 7) + 1
                    # Calculate current day (1-indexed)
                    plan.current_day = (days_since_start % 7) + 1
                else:
                    plan.current_week = 0
                    plan.current_day = 0
            else:
                plan.current_week = 0
                plan.current_day = 0

        # Pagination
        paginator = Paginator(queryset, 12)  # Show 12 plans per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get all users and workout plans for filters
        users = User.objects.all().order_by('username')
        workout_plans = WorkoutPlan.objects.all().order_by('name')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'user_workout_plans': page_obj,
            'users': users,
            'workout_plans': workout_plans,
            'selected_user': user_id,
            'selected_plan': workout_plan_id,
            'is_active': is_active,
            'is_modified': is_modified,
            'is_paginated': paginator.num_pages > 1,
            'page_obj': page_obj,
            'title': 'User Workout Plans',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_workout_plans.html', context)

    def view_user_workout_plan(self, request, plan_id):
        """View for viewing details of a user workout plan"""
        from workouts.models import UserWorkoutPlan, WorkoutSchedule, WorkoutDay
        from django.shortcuts import get_object_or_404

        user_workout_plan = get_object_or_404(UserWorkoutPlan.objects.select_related('user__user', 'workout_plan'), pk=plan_id)

        # Get the schedule for this plan
        schedule = WorkoutSchedule.objects.filter(user_workout_plan=user_workout_plan, week_number=1).first()

        if schedule:
            # Create a mapping of day numbers to workout days
            schedule.days_mapping = {}

            # First try to get days from the days JSON field
            if schedule.days:
                # Convert the days mapping to use day numbers as keys and workout day objects as values
                for day_num, day_id in schedule.days.items():
                    try:
                        if day_id is not None:
                            day = WorkoutDay.objects.get(id=day_id)
                            # Check if this day has been modified
                            day.is_modified = day.user_workout_plan_id is not None
                            schedule.days_mapping[int(day_num)] = day
                        else:
                            schedule.days_mapping[int(day_num)] = None
                    except (WorkoutDay.DoesNotExist, ValueError):
                        pass

            # If no days mapping found or it's empty, fall back to weekday fields
            if not schedule.days_mapping:
                weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                for i, day_field in enumerate(weekday_fields):
                    day_num = i + 1
                    workout_day = getattr(schedule, day_field)
                    if workout_day:
                        # Check if this day has been modified
                        workout_day.is_modified = workout_day.user_workout_plan_id is not None
                        schedule.days_mapping[day_num] = workout_day
                    else:
                        schedule.days_mapping[day_num] = None
        else:
            # If no schedule exists, create an empty one for the template
            schedule = type('EmptySchedule', (object,), {'id': None, 'days_mapping': {}})

        # Get modification details
        modifications = []
        if user_workout_plan.is_modified:
            # Add schedule modifications
            if schedule and any(day and day.is_modified for day in schedule.days_mapping.values()):
                modifications.append({
                    'type': 'Schedule',
                    'date': user_workout_plan.updated_at,
                    'description': 'The workout schedule has been customized with different workout days.',
                    'details': [f'Day {day_num}: {day.name} (Modified)' for day_num, day in schedule.days_mapping.items() if day and day.is_modified]
                })

            # Add workout day modifications
            modified_days = WorkoutDay.objects.filter(user_workout_plan=user_workout_plan)
            if modified_days.exists():
                modifications.append({
                    'type': 'Workout Days',
                    'date': user_workout_plan.updated_at,
                    'description': f'{modified_days.count()} workout days have been customized with different exercises or settings.',
                    'details': [day.name for day in modified_days]
                })

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'user_workout_plan': user_workout_plan,
            'schedule': schedule,
            'modifications': modifications,
            'title': f'User Workout Plan: {user_workout_plan.workout_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_workout_plan_detail.html', context)

    def edit_user_workout_plan_view(self, request, plan_id):
        """View for editing an existing user workout plan"""
        from workouts.models import UserWorkoutPlan, WorkoutSchedule, WorkoutDay
        from django.shortcuts import get_object_or_404
        from workouts.admin import UserWorkoutPlanAdmin
        from django import forms

        user_workout_plan = get_object_or_404(UserWorkoutPlan, pk=plan_id)

        # Create a form based on the UserWorkoutPlanAdmin form
        form_class = UserWorkoutPlanAdmin.CustomUserWorkoutPlanForm

        if request.method == 'POST':
            form = form_class(request.POST, request.FILES, instance=user_workout_plan)
            if form.is_valid():
                # Save the form
                user_workout_plan = form.save()

                # Handle sync options
                sync_option = form.cleaned_data.get('sync_option')
                if sync_option == 'all':
                    user_workout_plan.sync_with_global(overwrite_all=True)
                    messages.success(request, f"Successfully synced all parts of the plan with the global plan.")
                elif sync_option == 'unmodified':
                    user_workout_plan.sync_with_global(overwrite_all=False)
                    messages.success(request, f"Successfully synced unmodified parts of the plan.")
                elif sync_option == 'future':
                    user_workout_plan.sync_future_days()
                    messages.success(request, f"Successfully applied changes to future days only.")

                messages.success(request, f"User workout plan '{user_workout_plan}' updated successfully.")
                return redirect('acfit_admin:user-workout-plans')
        else:
            form = form_class(instance=user_workout_plan)

        # Get all schedules for this plan with days mapping
        schedules = WorkoutSchedule.objects.filter(user_workout_plan=user_workout_plan).order_by('week_number')

        # For each schedule, create a days mapping
        for schedule in schedules:
            # First try to get days from the days JSON field
            days_mapping = {}
            if schedule.days:
                # Convert the days mapping to use day numbers as keys and workout day objects as values
                for day_num, day_id in schedule.days.items():
                    try:
                        if day_id is not None:
                            day = WorkoutDay.objects.get(id=day_id)
                            days_mapping[int(day_num)] = day
                        else:
                            days_mapping[int(day_num)] = None
                    except (WorkoutDay.DoesNotExist, ValueError):
                        pass

            # If no days mapping found, fall back to weekday fields
            if not days_mapping:
                for i, day_field in enumerate(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']):
                    day_num = (schedule.week_number - 1) * 7 + i + 1
                    workout_day = getattr(schedule, day_field)
                    days_mapping[day_num] = workout_day

            schedule.days_mapping = days_mapping

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'plan_id': plan_id,
            'schedules': schedules,
            'title': f'Edit User Workout Plan: {user_workout_plan}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_workout_plan_form.html', context)

    def add_user_workout_plan_view(self, request):
        """View for adding a new user workout plan"""
        from workouts.models import UserWorkoutPlan
        from workouts.admin import UserWorkoutPlanAdmin
        from django import forms

        # Create a form based on the UserWorkoutPlanAdmin form
        form_class = UserWorkoutPlanAdmin.CustomUserWorkoutPlanForm

        if request.method == 'POST':
            form = form_class(request.POST, request.FILES)
            if form.is_valid():
                # Save the form
                user_workout_plan = form.save()
                messages.success(request, f"User workout plan '{user_workout_plan}' created successfully.")
                return redirect('acfit_admin:user-workout-plans')
        else:
            form = form_class()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Add User Workout Plan',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_workout_plan_form.html', context)

    def sync_user_workout_plan(self, request, plan_id):
        """View for syncing a user workout plan with its global plan"""
        from workouts.models import UserWorkoutPlan, WorkoutDay, WorkoutSection, ExerciseInstance
        from django.shortcuts import get_object_or_404, redirect
        from django.contrib import messages
        from django.db import transaction

        user_workout_plan = get_object_or_404(UserWorkoutPlan, pk=plan_id)
        global_plan = user_workout_plan.workout_plan

        if request.method == 'POST':
            sync_strategy = request.POST.get('sync_strategy', 'selective')

            try:
                with transaction.atomic():
                    if sync_strategy == 'full':
                        # Full sync - delete all customizations and recreate from global plan
                        # 1. Delete all custom workout days
                        WorkoutDay.objects.filter(user_workout_plan=user_workout_plan).delete()

                        # 2. Clone global plan days to user plan
                        global_days = WorkoutDay.objects.filter(workout_plan=global_plan, user_workout_plan__isnull=True)
                        for global_day in global_days:
                            # Clone the day
                            day_clone = WorkoutDay.objects.create(
                                name=global_day.name,
                                description=global_day.description,
                                day_number=global_day.day_number,
                                day_of_week=global_day.day_of_week,
                                total_duration=global_day.total_duration,
                                fitness_level=global_day.fitness_level,
                                calories_burn_estimate=global_day.calories_burn_estimate,
                                cover_image=global_day.cover_image,
                                workout_plan=global_plan,
                                user_workout_plan=user_workout_plan
                            )

                            # Clone sections
                            for section in WorkoutSection.objects.filter(workout_day=global_day):
                                section_clone = WorkoutSection.objects.create(
                                    name=section.name,
                                    section_type=section.section_type,
                                    order=section.order,
                                    duration=section.duration,
                                    notes=section.notes,
                                    workout_day=day_clone,
                                    user_workout_plan=user_workout_plan
                                )

                                # Clone exercises
                                for exercise in ExerciseInstance.objects.filter(workout_section=section):
                                    ExerciseInstance.objects.create(
                                        exercise=exercise.exercise,
                                        order=exercise.order,
                                        sets=exercise.sets,
                                        reps=exercise.reps,
                                        duration_seconds=exercise.duration_seconds,
                                        rest_seconds=exercise.rest_seconds,
                                        weight=exercise.weight,
                                        notes=exercise.notes,
                                        workout_section=section_clone,
                                        user_workout_plan=user_workout_plan
                                    )

                        messages.success(request, f'Successfully synced {user_workout_plan} with the global plan. All customizations have been overwritten.')

                    elif sync_strategy == 'preserve':
                        # Preserve modifications - only add new content
                        # 1. Find global days that don't exist in user plan
                        global_days = WorkoutDay.objects.filter(workout_plan=global_plan, user_workout_plan__isnull=True)
                        user_days = WorkoutDay.objects.filter(user_workout_plan=user_workout_plan)
                        user_day_numbers = set(user_days.values_list('day_number', flat=True))

                        # 2. Clone only new days
                        for global_day in global_days:
                            if global_day.day_number not in user_day_numbers:
                                # Clone the day
                                day_clone = WorkoutDay.objects.create(
                                    name=global_day.name,
                                    description=global_day.description,
                                    day_number=global_day.day_number,
                                    day_of_week=global_day.day_of_week,
                                    total_duration=global_day.total_duration,
                                    fitness_level=global_day.fitness_level,
                                    calories_burn_estimate=global_day.calories_burn_estimate,
                                    cover_image=global_day.cover_image,
                                    workout_plan=global_plan,
                                    user_workout_plan=user_workout_plan
                                )

                                # Clone sections
                                for section in WorkoutSection.objects.filter(workout_day=global_day):
                                    section_clone = WorkoutSection.objects.create(
                                        name=section.name,
                                        section_type=section.section_type,
                                        order=section.order,
                                        duration=section.duration,
                                        notes=section.notes,
                                        workout_day=day_clone,
                                        user_workout_plan=user_workout_plan
                                    )

                                    # Clone exercises
                                    for exercise in ExerciseInstance.objects.filter(workout_section=section):
                                        ExerciseInstance.objects.create(
                                            exercise=exercise.exercise,
                                            order=exercise.order,
                                            sets=exercise.sets,
                                            reps=exercise.reps,
                                            duration_seconds=exercise.duration_seconds,
                                            rest_seconds=exercise.rest_seconds,
                                            weight=exercise.weight,
                                            notes=exercise.notes,
                                            workout_section=section_clone,
                                            user_workout_plan=user_workout_plan
                                        )

                        messages.success(request, f'Successfully added new content from the global plan to {user_workout_plan}. Existing customizations have been preserved.')

                    else:  # selective
                        # Selective sync - update only selected components
                        sync_schedule = request.POST.get('sync_schedule') == 'true'
                        sync_workout_days = request.POST.get('sync_workout_days') == 'true'
                        sync_metadata = request.POST.get('sync_metadata') == 'true'

                        if sync_metadata:
                            # Update plan metadata (not implemented in this example)
                            pass

                        if sync_schedule:
                            # Update schedule (not implemented in this example)
                            pass

                        if sync_workout_days:
                            # Update workout days (not implemented in this example)
                            pass

                        messages.success(request, f'Successfully synced selected components of {user_workout_plan} with the global plan.')

                    # Mark as no longer modified
                    user_workout_plan.is_modified = False
                    user_workout_plan.save(update_fields=['is_modified'])

            except Exception as e:
                messages.error(request, f'Error syncing plan: {str(e)}')
                return redirect('acfit_admin:view-user-workout-plan', plan_id=plan_id)

            return redirect('acfit_admin:view-user-workout-plan', plan_id=plan_id)

        # If not POST, redirect to view page
        return redirect('acfit_admin:view-user-workout-plan', plan_id=plan_id)

    def add_workout_plan_view(self, request):
        """View for adding a new workout plan"""
        from workouts.models import WorkoutPlan
        from django import forms

        class WorkoutPlanForm(forms.ModelForm):
            class Meta:
                model = WorkoutPlan
                fields = [
                    # Basic Info
                    'name', 'description', 'cover_image',
                    # Plan Structure
                    'duration_weeks', 'workouts_per_week', 'workout_days',
                    # Targeting
                    'fitness_level', 'goal', 'location', 'requires_equipment',
                    'gender', 'age_group', 'health_conditions_allowed'
                ]

        if request.method == 'POST':
            form = WorkoutPlanForm(request.POST, request.FILES)
            if form.is_valid():
                workout_plan = form.save()
                messages.success(request, f'Workout plan "{workout_plan.name}" created successfully.')
                return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)
        else:
            form = WorkoutPlanForm()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Add Workout Plan',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_plan_form.html', context)

    def edit_workout_plan_view(self, request, plan_id):
        """View for editing an existing workout plan"""
        from workouts.models import WorkoutPlan, WorkoutDay, WorkoutSchedule
        from django import forms
        from django.shortcuts import get_object_or_404

        class WorkoutPlanForm(forms.ModelForm):
            SYNC_CHOICES = [
                ('none', 'Don\'t sync with any user plans (no changes)'),
                ('all', 'Sync everything with all user plans (overwrite all modifications)'),
                ('unmodified', 'Only sync with unmodified user plans (preserve custom plans)'),
                ('future', 'Only update future days in all user plans (preserve past/current days)')
            ]

            sync_option = forms.ChoiceField(
                choices=SYNC_CHOICES,
                required=False,
                initial='none',
                widget=forms.RadioSelect,
                help_text="Choose how to sync changes with user workout plans"
            )

            class Meta:
                model = WorkoutPlan
                fields = [
                    # Basic Info
                    'name', 'description', 'cover_image',
                    # Plan Structure
                    'duration_weeks', 'workouts_per_week', 'workout_days',
                    # Targeting
                    'fitness_level', 'goal', 'location', 'requires_equipment',
                    'gender', 'age_group', 'health_conditions_allowed'
                ]

        workout_plan = get_object_or_404(WorkoutPlan, id=plan_id)

        # Clean up any duplicate schedules before proceeding
        cleanup_count = WorkoutSchedule.cleanup_duplicates(workout_plan=workout_plan)
        if cleanup_count > 0:
            logger.warning(f"Cleaned up {cleanup_count} duplicate schedules for workout plan {plan_id}")
            messages.warning(request, f"Cleaned up {cleanup_count} duplicate schedules. Please refresh if you don't see your changes.")

        workout_days = WorkoutDay.objects.filter(workout_plan=workout_plan).order_by('day_number')
        schedules = WorkoutSchedule.objects.filter(workout_plan=workout_plan, user_workout_plan__isnull=True).order_by('week_number')

        # Prepare schedule data for template
        for schedule in schedules:
            # First try to get days from the days JSON field
            days_mapping = {}
            if schedule.days:
                # Convert the days mapping to use day numbers as keys and workout day objects as values
                for day_num, day_id in schedule.days.items():
                    try:
                        day = WorkoutDay.objects.get(id=day_id)
                        days_mapping[int(day_num)] = day
                    except (WorkoutDay.DoesNotExist, ValueError):
                        pass

            # If no days mapping found, fall back to weekday fields
            if not days_mapping:
                for i in range(1, 8):  # 7 days per week
                    day_field = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'][i-1]
                    day = getattr(schedule, day_field)
                    if day:
                        days_mapping[i] = day

            # Sort the days mapping by day number
            schedule.days_mapping = {k: days_mapping[k] for k in sorted(days_mapping.keys())}

        if request.method == 'POST':
            form = WorkoutPlanForm(request.POST, request.FILES, instance=workout_plan)
            if form.is_valid():
                # Save the form first
                saved_plan = form.save()

                # Get the current schedules before any sync operations
                current_schedules = list(WorkoutSchedule.objects.filter(
                    workout_plan=workout_plan,
                    user_workout_plan__isnull=True
                ).values_list('id', flat=True))

                # Log the current schedules
                logger.info(f"Current template schedules before sync: {current_schedules}")

                # Handle sync options
                sync_option = form.cleaned_data.get('sync_option')
                if sync_option != 'none':
                    # Get all user plans based on this global plan
                    from workouts.models import UserWorkoutPlan
                    from django.db import transaction

                    # Get user plans
                    user_plans = UserWorkoutPlan.objects.filter(workout_plan=workout_plan)

                    if sync_option == 'all':
                        # Sync all user plans, overwriting modifications
                        success_count = 0
                        error_count = 0
                        for user_plan in user_plans:
                            try:
                                # Each sync operation is in its own transaction
                                with transaction.atomic():
                                    user_plan.sync_with_global(overwrite_all=True)
                                    success_count += 1
                            except Exception as e:
                                error_count += 1
                                logger.error(f"Error syncing plan for user {user_plan.user.user.username}: {e}", exc_info=True)
                                messages.error(request, f"Error syncing plan for user {user_plan.user.user.username}'s profile: {str(e)}")

                        if success_count > 0:
                            messages.success(request, f"Successfully synced {success_count} user workout plans, overwriting modifications.")

                    elif sync_option == 'unmodified':
                        # Only sync unmodified user plans
                        success_count = 0
                        error_count = 0
                        for user_plan in user_plans:
                            try:
                                with transaction.atomic():
                                    user_plan.sync_with_global(overwrite_all=False)
                                    success_count += 1
                            except Exception as e:
                                error_count += 1
                                logger.error(f"Error syncing unmodified plan for user {user_plan.user.user.username}: {e}", exc_info=True)
                                messages.error(request, f"Error syncing plan for user {user_plan.user.user.username}'s profile: {str(e)}")

                        if success_count > 0:
                            messages.success(request, f"Successfully synced {success_count} unmodified user workout plans.")

                    elif sync_option == 'future':
                        # Only sync future days in all user plans
                        success_count = 0
                        error_count = 0
                        for user_plan in user_plans:
                            try:
                                with transaction.atomic():
                                    user_plan.sync_future_days()
                                    success_count += 1
                            except Exception as e:
                                error_count += 1
                                logger.error(f"Error syncing future days for user {user_plan.user.user.username}: {e}", exc_info=True)
                                messages.error(request, f"Error syncing future days for user {user_plan.user.user.username}'s profile: {str(e)}")

                        if success_count > 0:
                            messages.success(request, f"Successfully applied changes to future days in {success_count} user workout plans.")

                # Check if any schedules were deleted during the save operation
                remaining_schedules = list(WorkoutSchedule.objects.filter(
                    workout_plan=workout_plan,
                    user_workout_plan__isnull=True
                ).values_list('id', flat=True))

                # Log the remaining schedules
                logger.info(f"Remaining template schedules after sync: {remaining_schedules}")

                # Check for deleted schedules
                deleted_schedules = set(current_schedules) - set(remaining_schedules)
                if deleted_schedules:
                    logger.warning(f"Some template schedules were deleted during save: {deleted_schedules}")

                    # If we're not syncing with user plans, we need to restore the deleted schedules
                    if sync_option == 'none':
                        logger.info(f"Attempting to restore deleted schedules: {deleted_schedules}")
                        try:
                            # For each deleted schedule, try to restore it from the database backup
                            from django.db import connection
                            with transaction.atomic():
                                # Get the most recent backup of each deleted schedule
                                cursor = connection.cursor()
                                for schedule_id in deleted_schedules:
                                    # Try to find the deleted schedule in the database
                                    cursor.execute(
                                        """SELECT * FROM workouts_workoutschedule
                                           WHERE id = %s""",
                                        [schedule_id]
                                    )
                                    row = cursor.fetchone()
                                    if row:
                                        logger.info(f"Found deleted schedule {schedule_id} in database, attempting to restore")
                                        # Schedule still exists in database but was marked for deletion
                                        # Update it to make sure it's not deleted
                                        WorkoutSchedule.objects.filter(id=schedule_id).update(
                                            workout_plan=workout_plan,
                                            user_workout_plan=None
                                        )
                                    else:
                                        logger.warning(f"Could not find deleted schedule {schedule_id} in database")
                                        messages.warning(request, f"Could not restore deleted schedule (ID: {schedule_id}). You may need to recreate it manually.")
                        except Exception as e:
                            logger.error(f"Error restoring deleted schedules: {e}", exc_info=True)
                            messages.error(request, f"Error restoring deleted schedules: {str(e)}")

                messages.success(request, f'Workout plan "{workout_plan.name}" updated successfully.')
                return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)
        else:
            form = WorkoutPlanForm(instance=workout_plan)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'workout_plan': workout_plan,
            'workout_days': workout_days,
            'schedules': schedules,
            'title': f'Edit Workout Plan: {workout_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_plan_form.html', context)

    # Workout Day Views
    def add_workout_day_view(self, request, plan_id):
        """View for adding a new workout day"""
        from workouts.models import WorkoutPlan, WorkoutDay
        from django import forms
        from django.shortcuts import get_object_or_404

        workout_plan = get_object_or_404(WorkoutPlan, id=plan_id)

        class WorkoutDayForm(forms.ModelForm):
            class Meta:
                model = WorkoutDay
                fields = ['name', 'description', 'cover_image', 'day_number', 'calories_burn_estimate']

        if request.method == 'POST':
            form = WorkoutDayForm(request.POST, request.FILES)
            if form.is_valid():
                workout_day = form.save(commit=False)
                workout_day.workout_plan = workout_plan
                workout_day.save()
                messages.success(request, f'Workout day "{workout_day.name}" created successfully.')
                return redirect('acfit_admin:edit-workout-plan', plan_id=plan_id)
        else:
            # Get the next available day number
            existing_days = WorkoutDay.objects.filter(workout_plan=workout_plan).count()
            form = WorkoutDayForm(initial={'day_number': existing_days + 1})

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'workout_plan': workout_plan,
            'title': f'Add Workout Day to {workout_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_day_form.html', context)

    def edit_workout_day_view(self, request, day_id):
        """View for editing an existing workout day"""
        from workouts.models import WorkoutDay, Exercise, WorkoutSection, ExerciseInstance, WorkoutSession
        from django import forms
        from django.shortcuts import get_object_or_404
        import logging
        logger = logging.getLogger(__name__)

        try:
            workout_day = get_object_or_404(WorkoutDay, id=day_id)
            workout_plan = workout_day.workout_plan

            # Ensure workout_plan is not None
            if workout_plan is None:
                logger.warning(f"WorkoutDay {day_id} has no associated workout_plan")

            class WorkoutDayForm(forms.ModelForm):
                class Meta:
                    model = WorkoutDay
                    fields = ['name', 'description', 'cover_image', 'day_number', 'calories_burn_estimate']
        except Exception as e:
            messages.error(request, f'Error loading workout day: {e}')
            return redirect('admin:index')

        if request.method == 'POST':
            form = WorkoutDayForm(request.POST, request.FILES, instance=workout_day)
            if form.is_valid():
                form.save()
                messages.success(request, f'Workout day "{workout_day.name}" updated successfully.')
                if workout_plan and workout_plan.id:
                    return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)
                else:
                    return redirect('acfit_admin:workout-plans')
        else:
            form = WorkoutDayForm(instance=workout_day)

        # Get sessions for this day
        sessions = WorkoutSession.objects.filter(workout_day=workout_day).order_by('order')

        # Get sections for all sessions in this day
        sections = WorkoutSection.objects.filter(workout_session__in=sessions).order_by('workout_session', 'order')

        # Prepare section data for template
        section_exercises = ExerciseInstance.objects.filter(workout_section__in=sections).order_by('workout_section', 'order')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'workout_day': workout_day,
            'workout_plan': workout_plan,
            'sessions': sessions,
            'sections': sections,
            'section_exercises': section_exercises,
            'title': f'Edit Workout Day: {workout_day.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_day_form.html', context)

    def delete_workout_day_view(self, request, day_id):
        """View for deleting a workout day"""
        from workouts.models import WorkoutDay, WorkoutSession
        from django.shortcuts import get_object_or_404

        workout_day = get_object_or_404(WorkoutDay, id=day_id)
        workout_plan = workout_day.workout_plan

        # Get all sessions for this workout day
        sessions = WorkoutSession.objects.filter(workout_day=workout_day).order_by('order')

        if request.method == 'POST':
            workout_day.delete()
            messages.success(request, f'Workout day "{workout_day.name}" deleted successfully.')
            return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'workout_day': workout_day,
            'workout_plan': workout_plan,
            'sessions': sessions,
            'title': f'Delete Workout Day: {workout_day.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_day_delete.html', context)

    def add_workout_session_view(self, request, day_id):
        """View for adding a new workout session to a workout day"""
        from workouts.models import WorkoutDay, WorkoutSession
        from django import forms
        from django.shortcuts import get_object_or_404

        try:
            workout_day = get_object_or_404(WorkoutDay, id=day_id)

            # Get the next order number
            next_order = WorkoutSession.objects.filter(workout_day=workout_day).count() + 1

            class WorkoutSessionForm(forms.ModelForm):
                class Meta:
                    model = WorkoutSession
                    fields = ['name', 'description', 'duration', 'scheduled_time', 'order']
                    widgets = {
                        'description': forms.Textarea(attrs={'rows': 4}),
                        'scheduled_time': forms.TimeInput(attrs={'type': 'time'}, format='%H:%M'),
                    }

            if request.method == 'POST':
                form = WorkoutSessionForm(request.POST)
                if form.is_valid():
                    session = form.save(commit=False)
                    session.workout_day = workout_day
                    session.user_workout_plan = workout_day.user_workout_plan
                    session.save()
                    messages.success(request, f'Workout session "{session.name}" added successfully.')
                    return redirect('acfit_admin:edit-workout-day', day_id=workout_day.id)
            else:
                form = WorkoutSessionForm(initial={'order': next_order})

            # Add app_list for compatibility with admin templates
            app_list = self.get_app_list(request)

            context = {
                'form': form,
                'workout_day': workout_day,
                'workout_plan': workout_day.workout_plan,
                'title': f'Add Workout Session to {workout_day.name}',
                'app_list': app_list,
                'is_add': True,  # Flag to indicate we're adding a new session
                'sections': [],  # Empty sections list
                **self.each_context(request),
            }
            return TemplateResponse(request, 'admin/workout_session_form.html', context)
        except Exception as e:
            messages.error(request, f'Error adding workout session: {e}')
            return redirect('acfit_admin:dashboard')

    def edit_workout_session_view(self, request, session_id):
        """View for editing an existing workout session"""
        from workouts.models import WorkoutSession, WorkoutSection, ExerciseInstance
        from django import forms
        from django.shortcuts import get_object_or_404

        session = get_object_or_404(WorkoutSession, id=session_id)
        workout_day = session.workout_day

        class WorkoutSessionForm(forms.ModelForm):
            class Meta:
                model = WorkoutSession
                fields = ['name', 'description', 'duration', 'scheduled_time', 'order']
                widgets = {
                    'description': forms.Textarea(attrs={'rows': 4}),
                    'scheduled_time': forms.TimeInput(attrs={'type': 'time'}, format='%H:%M'),
                }

        if request.method == 'POST':
            form = WorkoutSessionForm(request.POST, instance=session)
            if form.is_valid():
                form.save()
                messages.success(request, f'Workout session "{session.name}" updated successfully.')
                return redirect('acfit_admin:edit-workout-day', day_id=workout_day.id)
        else:
            form = WorkoutSessionForm(instance=session)

        # Get sections for this session
        sections = WorkoutSection.objects.filter(workout_session=session).order_by('order')

        # Get exercises for these sections
        section_exercises = ExerciseInstance.objects.filter(workout_section__in=sections).order_by('workout_section', 'order')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'session': session,
            'workout_day': workout_day,
            'workout_plan': workout_day.workout_plan,
            'sections': sections,
            'section_exercises': section_exercises,
            'title': f'Edit Workout Session: {session.name}',
            'app_list': app_list,
            'is_add': False,  # Explicitly set is_add to False for edit view
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_session_form.html', context)

    def delete_workout_session_view(self, request, session_id):
        """View for deleting a workout session"""
        from workouts.models import WorkoutSession
        from django.shortcuts import get_object_or_404

        session = get_object_or_404(WorkoutSession, id=session_id)
        workout_day = session.workout_day

        if request.method == 'POST':
            session.delete()
            messages.success(request, f'Workout session "{session.name}" deleted successfully.')
            return redirect('acfit_admin:edit-workout-day', day_id=workout_day.id)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'session': session,
            'workout_day': workout_day,
            'workout_plan': workout_day.workout_plan,
            'title': f'Delete Workout Session: {session.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_session_delete.html', context)

    def add_workout_section_view(self, request, session_id):
        """View for adding a new workout section to a workout session"""
        from workouts.models import WorkoutSession, WorkoutSection
        from django import forms
        from django.shortcuts import get_object_or_404

        try:
            workout_session = get_object_or_404(WorkoutSession, id=session_id)

            class WorkoutSectionForm(forms.ModelForm):
                class Meta:
                    model = WorkoutSection
                    fields = ['name', 'section_type', 'order', 'notes', 'duration']
                    widgets = {
                        'notes': forms.Textarea(attrs={'rows': 4}),
                    }

            # Get the workout day from the session
            workout_day = workout_session.workout_day

            # Get the next order number
            next_order = WorkoutSection.objects.filter(workout_session=workout_session).count() + 1

            if request.method == 'POST':
                form = WorkoutSectionForm(request.POST)
                if form.is_valid():
                    section = form.save(commit=False)
                    section.workout_session = workout_session
                    section.user_workout_plan = workout_session.user_workout_plan
                    section.save()
                    messages.success(request, f'Workout section "{section.name}" added successfully.')
                    return redirect('acfit_admin:edit-workout-session', session_id=workout_session.id)
            else:
                form = WorkoutSectionForm(initial={'order': next_order})

            context = {
                'form': form,
                'workout_session': workout_session,
                'workout_day': workout_day,
                'workout_plan': workout_day.workout_plan,
                'title': f'Add Workout Section to {workout_session.name}',
                'app_list': self.get_app_list(request),
                **self.each_context(request),
            }

            return TemplateResponse(request, 'admin/workout_section_form.html', context)
        except Exception as e:
            messages.error(request, f'Error adding workout section: {e}')
            return redirect('acfit_admin:edit-workout-day', day_id=53)  # Fallback to workout day

    def edit_workout_section_view(self, request, section_id):
        """View for editing an existing workout section"""
        from workouts.models import WorkoutSection, ExerciseInstance
        from django import forms
        from django.shortcuts import get_object_or_404

        section = get_object_or_404(WorkoutSection, id=section_id)
        workout_session = section.workout_session
        workout_day = workout_session.workout_day

        class WorkoutSectionForm(forms.ModelForm):
            class Meta:
                model = WorkoutSection
                fields = ['name', 'section_type', 'order', 'notes', 'duration']
                widgets = {
                    'notes': forms.Textarea(attrs={'rows': 4}),
                }

        if request.method == 'POST':
            form = WorkoutSectionForm(request.POST, instance=section)
            if form.is_valid():
                form.save()
                messages.success(request, f'Workout section "{section.name}" updated successfully.')
                return redirect('acfit_admin:edit-workout-session', session_id=workout_session.id)
        else:
            form = WorkoutSectionForm(instance=section)

        # Get exercises for this section
        exercises = ExerciseInstance.objects.filter(workout_section=section).order_by('order')

        context = {
            'form': form,
            'section': section,
            'workout_session': workout_session,
            'workout_day': workout_day,
            'workout_plan': workout_day.workout_plan,
            'exercises': exercises,
            'title': f'Edit Workout Section: {section.name}',
            'app_list': self.get_app_list(request),
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/workout_section_form.html', context)

    def delete_workout_section_view(self, request, section_id):
        """View for deleting a workout section"""
        from workouts.models import WorkoutSection
        from django.shortcuts import get_object_or_404

        section = get_object_or_404(WorkoutSection, id=section_id)
        workout_session = section.workout_session
        workout_day = workout_session.workout_day

        if request.method == 'POST':
            section.delete()
            messages.success(request, f'Workout section "{section.name}" deleted successfully.')
            return redirect('acfit_admin:edit-workout-session', session_id=workout_session.id)

        context = {
            'section': section,
            'workout_session': workout_session,
            'workout_day': workout_day,
            'workout_plan': workout_day.workout_plan,
            'title': f'Delete Workout Section: {section.name}',
            'app_list': self.get_app_list(request),
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/delete_confirmation.html', context)

    def add_exercise_instance_view(self, request, section_id):
        """View for adding a new exercise instance to a workout section"""
        from workouts.models import WorkoutSection, ExerciseInstance, Exercise
        from django import forms
        from django.shortcuts import get_object_or_404

        section = get_object_or_404(WorkoutSection, id=section_id)
        # Get workout_day through workout_session
        workout_day = section.workout_session.workout_day if section.workout_session else None

        class ExerciseInstanceForm(forms.ModelForm):
            exercise = forms.ModelChoiceField(
                queryset=Exercise.objects.all().order_by('name'),
                widget=forms.Select(attrs={'class': 'select2'})
            )

            class Meta:
                model = ExerciseInstance
                fields = ['exercise', 'sets', 'reps', 'rest_seconds', 'duration_seconds', 'order', 'notes']
                widgets = {
                    'notes': forms.Textarea(attrs={'rows': 3}),
                }

        if request.method == 'POST':
            form = ExerciseInstanceForm(request.POST)
            if form.is_valid():
                exercise_instance = form.save(commit=False)
                exercise_instance.workout_section = section
                exercise_instance.save()
                messages.success(request, f'Exercise "{exercise_instance.exercise.name}" added successfully.')
                return redirect('acfit_admin:edit-workout-section', section_id=section.id)
        else:
            # Set default order to be the next number after the highest existing order
            next_order = ExerciseInstance.objects.filter(workout_section=section).count() + 1
            form = ExerciseInstanceForm(initial={'order': next_order, 'sets': 3, 'reps': 10, 'rest_seconds': 60})

        context = {
            'form': form,
            'section': section,
            'workout_day': workout_day,
            'title': f'Add Exercise to {section.name}',
            'app_list': self.get_app_list(request),
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/exercise_instance_form.html', context)

    def edit_exercise_instance_view(self, request, instance_id):
        """View for editing an existing exercise instance"""
        from workouts.models import ExerciseInstance, Exercise
        from django import forms
        from django.shortcuts import get_object_or_404

        exercise_instance = get_object_or_404(ExerciseInstance, id=instance_id)
        section = exercise_instance.workout_section
        # Get workout_day through workout_session
        workout_day = section.workout_session.workout_day if section.workout_session else None

        class ExerciseInstanceForm(forms.ModelForm):
            exercise = forms.ModelChoiceField(
                queryset=Exercise.objects.all().order_by('name'),
                widget=forms.Select(attrs={'class': 'select2'})
            )

            class Meta:
                model = ExerciseInstance
                fields = ['exercise', 'sets', 'reps', 'rest_seconds', 'duration_seconds', 'order', 'notes']
                widgets = {
                    'notes': forms.Textarea(attrs={'rows': 3}),
                }

        if request.method == 'POST':
            form = ExerciseInstanceForm(request.POST, instance=exercise_instance)
            if form.is_valid():
                form.save()
                messages.success(request, f'Exercise "{exercise_instance.exercise.name}" updated successfully.')
                return redirect('acfit_admin:edit-workout-section', section_id=section.id)
        else:
            form = ExerciseInstanceForm(instance=exercise_instance)

        context = {
            'form': form,
            'exercise_instance': exercise_instance,
            'section': section,
            'workout_day': workout_day,
            'title': f'Edit Exercise: {exercise_instance.exercise.name}',
            'app_list': self.get_app_list(request),
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/exercise_instance_form.html', context)

    def delete_exercise_instance_view(self, request, instance_id):
        """View for deleting an exercise instance"""
        from workouts.models import ExerciseInstance
        from django.shortcuts import get_object_or_404

        exercise_instance = get_object_or_404(ExerciseInstance, id=instance_id)
        section = exercise_instance.workout_section

        if request.method == 'POST':
            exercise_name = exercise_instance.exercise.name
            exercise_instance.delete()
            messages.success(request, f'Exercise "{exercise_name}" deleted successfully.')
            return redirect('acfit_admin:edit-workout-section', section_id=section.id)

        context = {
            'exercise_instance': exercise_instance,
            'section': section,
            'workout_day': section.workout_session.workout_day if section.workout_session else None,
            'title': f'Delete Exercise: {exercise_instance.exercise.name}',
            'app_list': self.get_app_list(request),
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/delete_confirmation.html', context)

    # Workout Schedule Views
    def add_workout_schedule_view(self, request, plan_id):
        """View for adding a new workout schedule"""
        from workouts.models import WorkoutPlan, WorkoutSchedule, WorkoutDay
        from django import forms
        from django.shortcuts import get_object_or_404
        from django.db import transaction
        import logging
        logger = logging.getLogger(__name__)

        workout_plan = get_object_or_404(WorkoutPlan, id=plan_id)
        workout_days = WorkoutDay.objects.filter(workout_plan=workout_plan).order_by('day_number')

        class WorkoutScheduleForm(forms.ModelForm):
            class Meta:
                model = WorkoutSchedule
                fields = ['week_number']

            # Add fields for each day of the week
            day_1 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_2 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_3 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_4 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_5 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_6 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_7 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")

            def clean_week_number(self):
                week_number = self.cleaned_data.get('week_number')

                # Check if this week number already exists for this workout plan
                existing = WorkoutSchedule.objects.filter(
                    workout_plan=workout_plan,
                    week_number=week_number
                )

                if existing.exists():
                    raise forms.ValidationError(f"Week {week_number} already exists for this workout plan. Please choose a different week number.")

                return week_number

        if request.method == 'POST':
            form = WorkoutScheduleForm(request.POST)
            if form.is_valid():
                try:
                    with transaction.atomic():
                        # Double-check for duplicate week numbers before creating
                        week_number = form.cleaned_data.get('week_number')
                        existing_schedule = WorkoutSchedule.objects.filter(
                            workout_plan=workout_plan,
                            week_number=week_number,
                            user_workout_plan__isnull=True
                        ).first()

                        if existing_schedule:
                            form.add_error('week_number', f'A schedule for week {week_number} already exists.')
                            messages.error(request, f'A schedule for week {week_number} already exists.')
                            logger.warning(f"Attempted to create duplicate schedule for week {week_number} in plan {plan_id}")
                        else:
                            # Check if at least one day is assigned
                            has_days = any([
                                form.cleaned_data.get('day_1'),
                                form.cleaned_data.get('day_2'),
                                form.cleaned_data.get('day_3'),
                                form.cleaned_data.get('day_4'),
                                form.cleaned_data.get('day_5'),
                                form.cleaned_data.get('day_6'),
                                form.cleaned_data.get('day_7')
                            ])

                            if not has_days:
                                logger.warning(f"Attempted to create empty schedule for week {week_number}")
                                messages.warning(request, f"Cannot create an empty schedule. Please assign at least one workout day.")
                                context = {
                                    'form': form,
                                    'workout_plan': workout_plan,
                                    'title': f'Add Weekly Schedule to {workout_plan.name}',
                                    'app_list': self.get_app_list(request),
                                    **self.each_context(request),
                                }
                                return TemplateResponse(request, 'admin/workout_schedule_form.html', context)

                            # Create new schedule
                            schedule = form.save(commit=False)
                            schedule.workout_plan = workout_plan
                            schedule.save()

                            # Save the days mapping
                            days_mapping = {}
                            for i in range(1, 8):
                                day = form.cleaned_data.get(f'day_{i}')
                                if day:
                                    days_mapping[str(i)] = day.id

                            schedule.days = days_mapping
                            schedule.save()

                            logger.info(f"Successfully created workout schedule for week {week_number} (ID: {schedule.id})")
                            messages.success(request, f'Workout schedule for Week {schedule.week_number} created successfully.')
                            return redirect('acfit_admin:edit-workout-plan', plan_id=plan_id)
                except Exception as e:
                    logger.error(f"Error creating workout schedule: {str(e)}", exc_info=True)
                    messages.error(request, f'Error creating workout schedule: {str(e)}')
        else:
            # Get the next available week number
            existing_schedules = WorkoutSchedule.objects.filter(workout_plan=workout_plan).count()
            form = WorkoutScheduleForm(initial={'week_number': existing_schedules + 1})

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'workout_plan': workout_plan,
            'title': f'Add Weekly Schedule to {workout_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_schedule_form.html', context)

    def edit_workout_schedule_view(self, request, schedule_id):
        """View for editing an existing workout schedule"""
        from workouts.models import WorkoutSchedule, WorkoutDay
        from django import forms
        from django.shortcuts import get_object_or_404

        schedule = get_object_or_404(WorkoutSchedule, id=schedule_id)
        workout_plan = schedule.workout_plan
        workout_days = WorkoutDay.objects.filter(workout_plan=workout_plan).order_by('day_number')

        class WorkoutScheduleForm(forms.ModelForm):
            class Meta:
                model = WorkoutSchedule
                fields = ['week_number']

            # Add fields for each day of the week
            day_1 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_2 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_3 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_4 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_5 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_6 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")
            day_7 = forms.ModelChoiceField(queryset=workout_days, required=False, empty_label="Rest Day")

            def clean_week_number(self):
                week_number = self.cleaned_data.get('week_number')

                # Check if this week number already exists for this workout plan (excluding current instance)
                existing = WorkoutSchedule.objects.filter(
                    workout_plan=workout_plan,
                    week_number=week_number,
                    user_workout_plan__isnull=True  # Only check template schedules
                ).exclude(id=schedule_id)

                if existing.exists():
                    raise forms.ValidationError(f"Week {week_number} already exists for this workout plan. Please choose a different week number.")

                return week_number

        # Prepare initial data for the form
        initial_data = {'week_number': schedule.week_number}
        days_mapping = schedule.days or {}

        for i in range(1, 8):
            day_id = days_mapping.get(str(i))
            if day_id:
                try:
                    day = WorkoutDay.objects.get(id=day_id)
                    initial_data[f'day_{i}'] = day
                except WorkoutDay.DoesNotExist:
                    pass

        if request.method == 'POST':
            form = WorkoutScheduleForm(request.POST, instance=schedule)
            if form.is_valid():
                # Check if week_number has changed and if there's already a schedule with that week number
                new_week_number = form.cleaned_data.get('week_number')
                if new_week_number != schedule.week_number:
                    # Check if there's another schedule with the same week number (excluding this one)
                    existing_schedule = WorkoutSchedule.objects.filter(
                        workout_plan=workout_plan,
                        week_number=new_week_number,
                        user_workout_plan__isnull=True
                    ).exclude(id=schedule.id).first()

                    if existing_schedule:
                        form.add_error('week_number', f'A schedule for week {new_week_number} already exists.')
                        messages.error(request, f'A schedule for week {new_week_number} already exists.')
                    else:
                        # Check if at least one day is assigned
                        has_days = any([
                            form.cleaned_data.get('day_1'),
                            form.cleaned_data.get('day_2'),
                            form.cleaned_data.get('day_3'),
                            form.cleaned_data.get('day_4'),
                            form.cleaned_data.get('day_5'),
                            form.cleaned_data.get('day_6'),
                            form.cleaned_data.get('day_7')
                        ])

                        if not has_days:
                            logger.warning(f"Attempted to save empty schedule for week {new_week_number}")
                            messages.warning(request, f"Cannot save an empty schedule. Please assign at least one workout day.")
                            context = {
                                'form': form,
                                'schedule': schedule,
                                'workout_plan': workout_plan,
                                'title': f'Edit Workout Schedule: Week {schedule.week_number}',
                                'app_list': app_list,
                                **self.each_context(request),
                            }
                            return TemplateResponse(request, 'admin/workout_schedule_form.html', context)

                        # Save the form if week number is unique
                        form.save()

                        # Save the days mapping
                        days_mapping = {}
                        for i in range(1, 8):
                            day = form.cleaned_data.get(f'day_{i}')
                            if day:
                                days_mapping[str(i)] = day.id

                        schedule.days = days_mapping
                        schedule.save()

                        messages.success(request, f'Workout schedule for Week {schedule.week_number} updated successfully.')
                        # Redirect to the appropriate page based on whether this is a user workout plan schedule or a global one
                        if schedule.user_workout_plan:
                            return redirect('acfit_admin:edit-user-workout-plan', plan_id=schedule.user_workout_plan.id)
                        else:
                            return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)
                else:
                    # Check if at least one day is assigned
                    has_days = any([
                        form.cleaned_data.get('day_1'),
                        form.cleaned_data.get('day_2'),
                        form.cleaned_data.get('day_3'),
                        form.cleaned_data.get('day_4'),
                        form.cleaned_data.get('day_5'),
                        form.cleaned_data.get('day_6'),
                        form.cleaned_data.get('day_7')
                    ])

                    if not has_days:
                        logger.warning(f"Attempted to save empty schedule for week {schedule.week_number}")
                        messages.warning(request, f"Cannot save an empty schedule. Please assign at least one workout day.")
                        context = {
                            'form': form,
                            'schedule': schedule,
                            'workout_plan': workout_plan,
                            'title': f'Edit Workout Schedule: Week {schedule.week_number}',
                            'app_list': app_list,
                            **self.each_context(request),
                        }
                        return TemplateResponse(request, 'admin/workout_schedule_form.html', context)

                    # Week number hasn't changed, safe to save
                    form.save()

                    # Save the days mapping
                    days_mapping = {}
                    for i in range(1, 8):
                        day = form.cleaned_data.get(f'day_{i}')
                        if day:
                            days_mapping[str(i)] = day.id

                    schedule.days = days_mapping
                    schedule.save()

                    messages.success(request, f'Workout schedule for Week {schedule.week_number} updated successfully.')
                    # Redirect to the appropriate page based on whether this is a user workout plan schedule or a global one
                    if schedule.user_workout_plan:
                        return redirect('acfit_admin:edit-user-workout-plan', plan_id=schedule.user_workout_plan.id)
                    else:
                        return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)
        else:
            form = WorkoutScheduleForm(instance=schedule, initial=initial_data)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'schedule': schedule,
            'workout_plan': workout_plan,
            'title': f'Edit Workout Schedule: Week {schedule.week_number}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_schedule_form.html', context)

    def delete_workout_schedule_view(self, request, schedule_id):
        """View for deleting a workout schedule"""
        from workouts.models import WorkoutSchedule, WorkoutDay, UserWorkoutPlan
        from django.shortcuts import get_object_or_404
        from django.db import transaction
        import logging
        import time
        logger = logging.getLogger(__name__)

        schedule = get_object_or_404(WorkoutSchedule, id=schedule_id)
        workout_plan = schedule.workout_plan
        week_number = schedule.week_number
        user_workout_plan = schedule.user_workout_plan

        if request.method == 'POST':
            try:
                # Add a small delay to prevent race conditions
                time.sleep(0.2)

                with transaction.atomic():
                    # Get locks on related objects to prevent concurrent modifications
                    if user_workout_plan:
                        user_workout_plan = UserWorkoutPlan.objects.select_for_update().get(id=user_workout_plan.id)
                    workout_plan = type(workout_plan).objects.select_for_update().get(id=workout_plan.id)

                    # Refresh the schedule with a lock
                    try:
                        schedule = WorkoutSchedule.objects.select_for_update().get(id=schedule_id)
                    except WorkoutSchedule.DoesNotExist:
                        # Schedule was already deleted
                        logger.warning(f"Workout schedule {schedule_id} was already deleted")
                        messages.warning(request, f'Workout schedule was already deleted.')
                        return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)

                    # Check if this is a template schedule (not user-specific)
                    if schedule.user_workout_plan is None:
                        # Check if there are any user-specific schedules that reference this template
                        user_schedules = WorkoutSchedule.objects.filter(
                            workout_plan=workout_plan,
                            week_number=week_number,
                            user_workout_plan__isnull=False
                        )
                        if user_schedules.exists():
                            logger.warning(f"Deleting template schedule {schedule_id} that has {user_schedules.count()} user-specific references")
                            messages.warning(request, f'This schedule is referenced by {user_schedules.count()} user plans. Deleting it may affect those plans.')

                    # Delete the schedule
                    schedule.delete()
                    logger.info(f"Successfully deleted workout schedule {schedule_id} (Week {week_number})")
                    messages.success(request, f'Workout schedule for Week {week_number} deleted successfully.')

                    # Check for any duplicate schedules with the same week number
                    if user_workout_plan:
                        duplicates = WorkoutSchedule.objects.filter(
                            user_workout_plan=user_workout_plan,
                            week_number=week_number
                        )
                        if duplicates.exists():
                            logger.warning(f"Found {duplicates.count()} duplicate schedules for week {week_number} after deletion")
                            messages.warning(request, f'Found {duplicates.count()} duplicate schedules for week {week_number}. Please check and fix if needed.')
            except Exception as e:
                logger.error(f"Error deleting workout schedule {schedule_id}: {str(e)}", exc_info=True)
                messages.error(request, f'Error deleting workout schedule: {str(e)}')

            return redirect('acfit_admin:edit-workout-plan', plan_id=workout_plan.id)

        # Prepare workout days info for the template
        workout_days_info = []
        if schedule.days:
            for day_num, workout_day_id in schedule.days.items():
                day_info = {'name': None}
                if workout_day_id:
                    try:
                        workout_day = WorkoutDay.objects.get(id=workout_day_id)
                        day_info['name'] = workout_day.name
                    except WorkoutDay.DoesNotExist:
                        pass
                workout_days_info.append((day_num, day_info))
            # Sort by day number
            workout_days_info.sort(key=lambda x: int(x[0]))

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'schedule': schedule,
            'workout_plan': workout_plan,
            'workout_days_info': workout_days_info,
            'title': f'Delete Workout Schedule: Week {schedule.week_number}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/workout_schedule_delete.html', context)

    # Meal Plan Views
    def meal_plans_view(self, request):
        """View for listing all meal plans"""
        from meals.models import MealPlan

        meal_plans = MealPlan.objects.all().order_by('-created_at')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'meal_plans': meal_plans,
            'title': 'Meal Plans',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/meal_plans.html', context)

    def add_meal_plan_view(self, request):
        """View for adding a new meal plan"""
        from meals.models import MealPlan
        from django import forms

        class MealPlanForm(forms.ModelForm):
            class Meta:
                model = MealPlan
                fields = [
                    # Basic Info
                    'name', 'description', 'cover_image',
                    # Plan Structure
                    'duration_weeks',
                    # Nutritional Targets
                    'daily_calories', 'daily_protein', 'daily_carbs', 'daily_fat',
                    # Diet Type
                    'is_keto', 'is_intermittent_fasting',
                    # Targeting
                    'goal', 'gender', 'age_group', 'cooking_preference',
                    'water_intake', 'health_conditions_allowed'
                ]

        if request.method == 'POST':
            form = MealPlanForm(request.POST, request.FILES)
            if form.is_valid():
                meal_plan = form.save()
                messages.success(request, f'Meal plan "{meal_plan.name}" created successfully.')
                return redirect('acfit_admin:edit-meal-plan', plan_id=meal_plan.id)
        else:
            form = MealPlanForm()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Add Meal Plan',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/meal_plan_form.html', context)

    def edit_user_meal_plan_view(self, request, plan_id):
        """View for editing an existing user meal plan"""
        from meals.models import UserMealPlan, MealSchedule
        from django.shortcuts import get_object_or_404
        from meals.admin import UserMealPlanAdmin

        user_meal_plan = get_object_or_404(UserMealPlan, pk=plan_id)

        # Create a form based on the UserMealPlanAdmin form
        form_class = UserMealPlanAdmin.CustomUserMealPlanForm

        if request.method == 'POST':
            form = form_class(request.POST, request.FILES, instance=user_meal_plan)
            if form.is_valid():
                # Save the form
                user_meal_plan = form.save()

                # Handle sync options
                sync_option = form.cleaned_data.get('sync_option')
                if sync_option == 'all':
                    user_meal_plan.sync_with_global(overwrite_all=True)
                    messages.success(request, f"Successfully synced all parts of the plan with the global plan.")
                elif sync_option == 'unmodified':
                    user_meal_plan.sync_with_global(overwrite_all=False)
                    messages.success(request, f"Successfully synced unmodified parts of the plan.")
                elif sync_option == 'future':
                    user_meal_plan.sync_future_days()
                    messages.success(request, f"Successfully applied changes to future days only.")

                messages.success(request, f"User meal plan '{user_meal_plan}' updated successfully.")
                return redirect('acfit_admin:user-meal-plans')
        else:
            form = form_class(instance=user_meal_plan)

        # Get all schedules for this plan with days mapping
        schedules = MealSchedule.objects.filter(user_meal_plan=user_meal_plan).order_by('week_number')

        # For each schedule, create a days mapping
        for schedule in schedules:
            days_mapping = {}
            for i, day_field in enumerate(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']):
                day_num = (schedule.week_number - 1) * 7 + i + 1
                meal_day = getattr(schedule, day_field)
                days_mapping[day_num] = meal_day
            schedule.days_mapping = days_mapping

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'plan_id': plan_id,
            'schedules': schedules,
            'title': f'Edit User Meal Plan: {user_meal_plan}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_meal_plan_form.html', context)

    def add_user_meal_plan_view(self, request):
        """View for adding a new user meal plan"""
        from meals.admin import UserMealPlanAdmin

        # Create a form based on the UserMealPlanAdmin form
        form_class = UserMealPlanAdmin.CustomUserMealPlanForm

        if request.method == 'POST':
            form = form_class(request.POST, request.FILES)
            if form.is_valid():
                # Save the form
                user_meal_plan = form.save()
                messages.success(request, f"User meal plan '{user_meal_plan}' created successfully.")
                return redirect('acfit_admin:user-meal-plans')
        else:
            form = form_class()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Add User Meal Plan',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_meal_plan_form.html', context)

    def user_meal_plans_view(self, request):
        """View for listing all user meal plans"""
        from meals.models import UserMealPlan, MealPlan
        from django.contrib.auth import get_user_model
        from django.core.paginator import Paginator

        User = get_user_model()

        # Get filter parameters
        user_id = request.GET.get('user')
        meal_plan_id = request.GET.get('meal_plan')
        is_active = request.GET.get('is_active')
        is_modified = request.GET.get('is_modified')

        # Base queryset
        queryset = UserMealPlan.objects.select_related('user__user', 'meal_plan').order_by('-start_date')

        # Apply filters
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        if meal_plan_id:
            queryset = queryset.filter(meal_plan_id=meal_plan_id)
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)
        if is_modified == 'true':
            queryset = queryset.filter(is_modified=True)
        elif is_modified == 'false':
            queryset = queryset.filter(is_modified=False)

        # Pagination
        paginator = Paginator(queryset, 12)  # Show 12 plans per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get all users and meal plans for filters
        users = User.objects.all().order_by('username')
        meal_plans = MealPlan.objects.all().order_by('name')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'user_meal_plans': page_obj,
            'users': users,
            'meal_plans': meal_plans,
            'selected_user': user_id,
            'selected_plan': meal_plan_id,
            'is_active': is_active,
            'is_modified': is_modified,
            'is_paginated': paginator.num_pages > 1,
            'page_obj': page_obj,
            'title': 'User Meal Plans',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_meal_plans.html', context)

    def view_user_meal_plan(self, request, plan_id):
        """View for viewing details of a user meal plan"""
        from meals.models import UserMealPlan, MealSchedule, DailyMealPlan
        from django.shortcuts import get_object_or_404

        user_meal_plan = get_object_or_404(UserMealPlan.objects.select_related('user__user', 'meal_plan'), pk=plan_id)

        # Get the schedule for this plan
        schedule = MealSchedule.objects.filter(user_meal_plan=user_meal_plan, week_number=1).first()

        # If schedule exists, create a days mapping
        if schedule:
            days_mapping = {}
            for i, day_field in enumerate(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']):
                day_num = i + 1
                meal_day = getattr(schedule, day_field)
                days_mapping[day_num] = meal_day
            schedule.days_mapping = days_mapping

        # Get modification details
        modifications = []
        if user_meal_plan.is_modified:
            # Add general modification info
            modifications.append({
                'type': 'General',
                'date': user_meal_plan.updated_at,
                'description': 'This meal plan has been modified from the global template.',
                'details': []
            })

            # Add meal day modifications
            modified_days = DailyMealPlan.objects.filter(user_meal_plan=user_meal_plan)
            if modified_days.exists():
                modifications.append({
                    'type': 'Meal Days',
                    'date': user_meal_plan.updated_at,
                    'description': f'{modified_days.count()} meal days have been customized with different meals or settings.',
                    'details': [day.name for day in modified_days]
                })

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'user_meal_plan': user_meal_plan,
            'schedule': schedule,
            'modifications': modifications,
            'title': f'User Meal Plan: {user_meal_plan.meal_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_meal_plan_detail.html', context)

    def sync_user_meal_plan(self, request, plan_id):
        """View for syncing a user meal plan with its global plan"""
        from meals.models import UserMealPlan, DailyMealPlan
        from django.shortcuts import get_object_or_404, redirect
        from django.contrib import messages
        from django.db import transaction

        user_meal_plan = get_object_or_404(UserMealPlan, pk=plan_id)
        global_plan = user_meal_plan.meal_plan

        if request.method == 'POST':
            sync_strategy = request.POST.get('sync_strategy', 'selective')

            with transaction.atomic():
                if sync_strategy == 'full':
                    # Full sync - overwrite all modifications
                    user_meal_plan.sync_with_global(overwrite_all=True)
                    messages.success(request, f"Successfully synced all parts of the plan with the global plan.")
                elif sync_strategy == 'preserve':
                    # Preserve modifications - only add new content
                    user_meal_plan.sync_with_global(overwrite_all=False)
                    messages.success(request, f"Successfully synced unmodified parts of the plan.")
                elif sync_strategy == 'selective':
                    # Selective sync - only sync selected components
                    components = request.POST.getlist('components', [])

                    if 'structure' in components:
                        # Sync plan structure (weeks, days)
                        # Implementation depends on your specific needs
                        pass

                    if 'nutrition' in components:
                        # Sync nutritional targets
                        user_meal_plan.daily_calories = global_plan.daily_calories
                        user_meal_plan.daily_protein = global_plan.daily_protein
                        user_meal_plan.daily_carbs = global_plan.daily_carbs
                        user_meal_plan.daily_fat = global_plan.daily_fat
                        user_meal_plan.save(update_fields=['daily_calories', 'daily_protein', 'daily_carbs', 'daily_fat'])

                    if 'future_days' in components:
                        # Only sync future days
                        user_meal_plan.sync_future_days()

                    messages.success(request, f"Successfully synced selected components with the global plan.")

        return redirect('acfit_admin:view-user-meal-plan', plan_id=plan_id)

    def edit_meal_plan_view(self, request, plan_id):
        """View for editing an existing meal plan"""
        from meals.models import MealPlan, DailyMealPlan, MealSchedule
        from django import forms
        from django.shortcuts import get_object_or_404

        class MealPlanForm(forms.ModelForm):
            SYNC_CHOICES = [
                ('none', 'Don\'t sync with any user plans (no changes)'),
                ('all', 'Sync everything with all user plans (overwrite all modifications)'),
                ('unmodified', 'Only sync with unmodified user plans (preserve custom plans)'),
                ('future', 'Only update future days in all user plans (preserve past/current days)')
            ]

            sync_option = forms.ChoiceField(
                choices=SYNC_CHOICES,
                required=False,
                initial='none',
                widget=forms.RadioSelect,
                help_text="Choose how to sync changes with user meal plans"
            )

            class Meta:
                model = MealPlan
                fields = [
                    # Basic Info
                    'name', 'description', 'cover_image',
                    # Plan Structure
                    'duration_weeks',
                    # Nutritional Targets
                    'daily_calories', 'daily_protein', 'daily_carbs', 'daily_fat',
                    # Diet Type
                    'is_keto', 'is_intermittent_fasting',
                    # Targeting
                    'goal', 'gender', 'age_group', 'cooking_preference',
                    'water_intake', 'health_conditions_allowed'
                ]

        meal_plan = get_object_or_404(MealPlan, id=plan_id)
        daily_plans = DailyMealPlan.objects.filter(meal_plan=meal_plan).order_by('day_number')
        schedules = MealSchedule.objects.filter(meal_plan=meal_plan).order_by('week_number')

        # Prepare schedule data for template
        for schedule in schedules:
            days_mapping = {}
            for i in range(1, 8):  # 7 days per week
                day_field = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'][i-1]
                day = getattr(schedule, day_field)
                days_mapping[i] = day
            schedule.days_mapping = days_mapping

        if request.method == 'POST':
            try:
                form = MealPlanForm(request.POST, request.FILES, instance=meal_plan)
                if form.is_valid():
                    # Save the form first without handling sync options
                    form.save()

                    # Handle sync options separately
                    sync_option = form.cleaned_data.get('sync_option')
                    if sync_option != 'none':
                        # Get all user plans based on this global plan
                        from meals.models import UserMealPlan
                        user_plans = UserMealPlan.objects.filter(meal_plan=meal_plan)

                        if sync_option == 'all':
                            # Sync all user plans, overwriting modifications
                            for user_plan in user_plans:
                                try:
                                    user_plan.sync_with_global(overwrite_all=True)
                                except Exception as sync_error:
                                    logger.error(f"Error syncing user plan {user_plan.id}: {str(sync_error)}")
                                    messages.warning(request, f"Error syncing plan for user {user_plan.user}: {str(sync_error)}")
                            messages.success(request, f"Successfully synced all user meal plans, overwriting modifications.")

                        elif sync_option == 'unmodified':
                            # Only sync unmodified user plans
                            for user_plan in user_plans:
                                try:
                                    user_plan.sync_with_global(overwrite_all=False)
                                except Exception as sync_error:
                                    logger.error(f"Error syncing user plan {user_plan.id}: {str(sync_error)}")
                                    messages.warning(request, f"Error syncing plan for user {user_plan.user}: {str(sync_error)}")
                            messages.success(request, f"Successfully synced unmodified user meal plans.")

                        elif sync_option == 'future':
                            # Only sync future days in all user plans
                            for user_plan in user_plans:
                                try:
                                    user_plan.sync_future_days()
                                except Exception as sync_error:
                                    logger.error(f"Error syncing future days for user plan {user_plan.id}: {str(sync_error)}")
                                    messages.warning(request, f"Error syncing future days for user {user_plan.user}: {str(sync_error)}")
                            messages.success(request, f"Successfully applied changes to future days in all user meal plans.")

                    messages.success(request, f'Meal plan "{meal_plan.name}" updated successfully.')
                    return redirect('acfit_admin:edit-meal-plan', plan_id=meal_plan.id)
            except Exception as e:
                # Handle the IntegrityError or any other exception
                logger.error(f"Error updating meal plan {plan_id}: {str(e)}")
                messages.error(request, f"Error updating meal plan: {str(e)}")
                # Re-fetch the data to ensure it's up to date
                meal_plan = get_object_or_404(MealPlan, id=plan_id)
                daily_plans = DailyMealPlan.objects.filter(meal_plan=meal_plan).order_by('day_number')
                schedules = MealSchedule.objects.filter(meal_plan=meal_plan).order_by('week_number')

                # Prepare schedule data for template
                for schedule in schedules:
                    days_mapping = {}
                    for i in range(1, 8):  # 7 days per week
                        day_field = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'][i-1]
                        day = getattr(schedule, day_field)
                        days_mapping[i] = day
                    schedule.days_mapping = days_mapping
        else:
            form = MealPlanForm(instance=meal_plan)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'meal_plan': meal_plan,
            'daily_plans': daily_plans,
            'schedules': schedules,
            'title': f'Edit Meal Plan: {meal_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/meal_plan_form.html', context)

    # Daily Meal Plan Views
    def add_daily_meal_plan_view(self, request, plan_id):
        """View for adding a new daily meal plan"""
        from meals.models import MealPlan, DailyMealPlan, Meal
        from django import forms
        from django.shortcuts import get_object_or_404

        meal_plan = get_object_or_404(MealPlan, id=plan_id)
        meals = Meal.objects.all().order_by('name')

        class DailyMealPlanForm(forms.ModelForm):
            class Meta:
                model = DailyMealPlan
                fields = ['name', 'day_number']

            breakfast = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Breakfast")
            lunch = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Lunch")
            dinner = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Dinner")
            snack_1 = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Snack")
            snack_2 = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Snack")

        if request.method == 'POST':
            form = DailyMealPlanForm(request.POST)
            if form.is_valid():
                # Check if a daily meal plan with the same meal plan and day number already exists
                day_number = form.cleaned_data.get('day_number')
                existing_plan = DailyMealPlan.objects.filter(
                    meal_plan=meal_plan,
                    day_number=day_number
                ).first()

                if existing_plan:
                    # If it exists, show an error message
                    messages.error(
                        request,
                        f'A daily meal plan for Day {day_number} already exists. '
                        f'Please edit the existing plan or choose a different day number.'
                    )
                else:
                    # If it doesn't exist, create a new one
                    daily_plan = form.save(commit=False)
                    daily_plan.meal_plan = meal_plan

                    # Set day_of_week based on day_number (for backward compatibility)
                    # Use modulo to map day_number to 0-6 (0=Monday, 6=Sunday)
                    daily_plan.day_of_week = (day_number - 1) % 7

                    daily_plan.save()

                    # Save the meal relationships
                    if form.cleaned_data.get('breakfast'):
                        daily_plan.breakfast = form.cleaned_data.get('breakfast')
                    if form.cleaned_data.get('lunch'):
                        daily_plan.lunch = form.cleaned_data.get('lunch')
                    if form.cleaned_data.get('dinner'):
                        daily_plan.dinner = form.cleaned_data.get('dinner')

                    # Only set snack fields if they exist in the model
                    try:
                        if form.cleaned_data.get('snack_1') and hasattr(daily_plan, 'snack_1'):
                            daily_plan.snack_1 = form.cleaned_data.get('snack_1')
                        if form.cleaned_data.get('snack_2') and hasattr(daily_plan, 'snack_2'):
                            daily_plan.snack_2 = form.cleaned_data.get('snack_2')
                    except Exception as e:
                        # Log the error but continue
                        print(f"Warning: Could not set snack fields: {e}")

                    daily_plan.save()

                    messages.success(request, f'Daily meal plan for Day {daily_plan.day_number} created successfully.')
                    return redirect('acfit_admin:edit-meal-plan', plan_id=plan_id)


        else:
            # Get the next available day number
            existing_days = DailyMealPlan.objects.filter(meal_plan=meal_plan).count()
            form = DailyMealPlanForm(initial={'day_number': existing_days + 1, 'name': f'Day {existing_days + 1}'})

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'meal_plan': meal_plan,
            'title': f'Add Daily Meal Plan to {meal_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/daily_meal_plan_form.html', context)

    def edit_daily_meal_plan_view(self, request, day_id):
        """View for editing an existing daily meal plan"""
        from meals.models import DailyMealPlan, Meal
        from django import forms
        from django.shortcuts import get_object_or_404

        daily_plan = get_object_or_404(DailyMealPlan, id=day_id)
        meal_plan = daily_plan.meal_plan
        meals = Meal.objects.all().order_by('name')

        class DailyMealPlanForm(forms.ModelForm):
            class Meta:
                model = DailyMealPlan
                fields = ['name', 'day_number']

            breakfast = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Breakfast")
            lunch = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Lunch")
            dinner = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Dinner")
            snack_1 = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Snack")
            snack_2 = forms.ModelChoiceField(queryset=meals, required=False, empty_label="No Snack")

        # Prepare initial data for the form
        initial_data = {
            'name': daily_plan.name,
            'day_number': daily_plan.day_number,
            'breakfast': daily_plan.breakfast,
            'lunch': daily_plan.lunch,
            'dinner': daily_plan.dinner,
        }

        # Only add snack fields if they exist in the model
        try:
            if hasattr(daily_plan, 'snack_1'):
                initial_data['snack_1'] = daily_plan.snack_1
            if hasattr(daily_plan, 'snack_2'):
                initial_data['snack_2'] = daily_plan.snack_2
        except Exception as e:
            # Log the error but continue
            print(f"Warning: Could not get snack fields for initial data: {e}")

        if request.method == 'POST':
            form = DailyMealPlanForm(request.POST, instance=daily_plan)
            if form.is_valid():
                # Check if day number is being changed to one that already exists
                new_day_number = form.cleaned_data.get('day_number')
                if new_day_number != daily_plan.day_number:
                    existing_plan = DailyMealPlan.objects.filter(
                        meal_plan=meal_plan,
                        day_number=new_day_number
                    ).exclude(id=daily_plan.id).first()

                    if existing_plan:
                        # If it exists, show an error message
                        messages.error(
                            request,
                            f'A daily meal plan for Day {new_day_number} already exists. '
                            f'Please choose a different day number.'
                        )
                        return redirect('acfit_admin:edit-daily-meal-plan', day_id=daily_plan.id)

                # Save the form
                form.save()

                # Update day_of_week based on day_number (for backward compatibility)
                daily_plan.day_of_week = (new_day_number - 1) % 7

                # Save the meal relationships
                daily_plan.breakfast = form.cleaned_data.get('breakfast')
                daily_plan.lunch = form.cleaned_data.get('lunch')
                daily_plan.dinner = form.cleaned_data.get('dinner')
                # Only set snack fields if they exist in the model
                try:
                    if hasattr(daily_plan, 'snack_1'):
                        daily_plan.snack_1 = form.cleaned_data.get('snack_1')
                    if hasattr(daily_plan, 'snack_2'):
                        daily_plan.snack_2 = form.cleaned_data.get('snack_2')
                except Exception as e:
                    # Log the error but continue
                    print(f"Warning: Could not set snack fields: {e}")
                daily_plan.save()

                messages.success(request, f'Daily meal plan for Day {daily_plan.day_number} updated successfully.')
                # Check if this is a user meal plan or global meal plan
                if daily_plan.user_meal_plan:
                    return redirect('acfit_admin:view-user-meal-plan', plan_id=daily_plan.user_meal_plan.id)
                elif meal_plan:
                    return redirect('acfit_admin:edit-meal-plan', plan_id=meal_plan.id)
                else:
                    # Fallback to meal plans list if no parent plan is found
                    return redirect('acfit_admin:meal-plans')
        else:
            form = DailyMealPlanForm(instance=daily_plan, initial=initial_data)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'daily_plan': daily_plan,
            'meal_plan': meal_plan,
            'title': f'Edit Daily Meal Plan: Day {daily_plan.day_number}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/daily_meal_plan_form.html', context)

    def delete_daily_meal_plan_view(self, request, day_id):
        """View for deleting a daily meal plan"""
        from meals.models import DailyMealPlan
        from django.shortcuts import get_object_or_404

        daily_plan = get_object_or_404(DailyMealPlan, id=day_id)
        meal_plan = daily_plan.meal_plan
        user_meal_plan = daily_plan.user_meal_plan
        day_number = daily_plan.day_number  # Store day number before deletion

        if request.method == 'POST':
            daily_plan.delete()
            messages.success(request, f'Daily meal plan for Day {day_number} deleted successfully.')

            # Determine where to redirect based on the plan type
            if user_meal_plan:
                return redirect('acfit_admin:view-user-meal-plan', plan_id=user_meal_plan.id)
            elif meal_plan:
                return redirect('acfit_admin:edit-meal-plan', plan_id=meal_plan.id)
            else:
                # Fallback to meal plans list if no parent plan is found
                return redirect('acfit_admin:meal-plans')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'daily_plan': daily_plan,
            'meal_plan': meal_plan,
            'title': f'Delete Daily Meal Plan: Day {daily_plan.day_number}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/daily_meal_plan_delete.html', context)

    # Meal Schedule Views
    def add_meal_schedule_view(self, request, plan_id):
        """View for adding a new meal schedule"""
        from meals.models import MealPlan, MealSchedule, DailyMealPlan
        from django import forms
        from django.shortcuts import get_object_or_404

        meal_plan = get_object_or_404(MealPlan, id=plan_id)
        daily_plans = DailyMealPlan.objects.filter(meal_plan=meal_plan).order_by('day_number')

        class MealScheduleForm(forms.ModelForm):
            class Meta:
                model = MealSchedule
                fields = ['week_number']

            # Add fields for each day of the week
            day_1 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_2 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_3 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_4 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_5 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_6 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_7 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")

            def clean_week_number(self):
                week_number = self.cleaned_data.get('week_number')

                # Check if this week number already exists for this meal plan
                existing = MealSchedule.objects.filter(
                    meal_plan=meal_plan,
                    week_number=week_number,
                    user_meal_plan__isnull=True  # Only check template schedules
                )

                if existing.exists():
                    raise forms.ValidationError(f"Week {week_number} already exists for this meal plan. Please choose a different week number.")

                return week_number

        if request.method == 'POST':
            form = MealScheduleForm(request.POST)
            if form.is_valid():
                schedule = form.save(commit=False)
                schedule.meal_plan = meal_plan
                schedule.save()

                # Save the days mapping
                days_mapping = {}
                for i in range(1, 8):
                    day = form.cleaned_data.get(f'day_{i}')
                    if day:
                        days_mapping[str(i)] = day.id

                schedule.days = days_mapping
                schedule.save()

                messages.success(request, f'Meal schedule for Week {schedule.week_number} created successfully.')
                return redirect('acfit_admin:edit-meal-plan', plan_id=plan_id)
        else:
            # Get the next available week number
            existing_schedules = MealSchedule.objects.filter(meal_plan=meal_plan).count()
            form = MealScheduleForm(initial={'week_number': existing_schedules + 1})

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'meal_plan': meal_plan,
            'title': f'Add Weekly Schedule to {meal_plan.name}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/meal_schedule_form.html', context)

    def edit_meal_schedule_view(self, request, schedule_id):
        """View for editing an existing meal schedule"""
        from meals.models import MealSchedule, DailyMealPlan
        from django import forms
        from django.shortcuts import get_object_or_404

        schedule = get_object_or_404(MealSchedule, id=schedule_id)
        meal_plan = schedule.meal_plan
        daily_plans = DailyMealPlan.objects.filter(meal_plan=meal_plan).order_by('day_number')

        class MealScheduleForm(forms.ModelForm):
            class Meta:
                model = MealSchedule
                fields = ['week_number']

            # Add fields for each day of the week
            day_1 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_2 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_3 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_4 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_5 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_6 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")
            day_7 = forms.ModelChoiceField(queryset=daily_plans, required=False, empty_label="Not Assigned")

            def clean_week_number(self):
                week_number = self.cleaned_data.get('week_number')

                # Check if this week number already exists for this meal plan (excluding current instance)
                existing = MealSchedule.objects.filter(
                    meal_plan=meal_plan,
                    week_number=week_number,
                    user_meal_plan__isnull=True  # Only check template schedules
                ).exclude(id=schedule.id)

                if existing.exists():
                    raise forms.ValidationError(f"Week {week_number} already exists for this meal plan. Please choose a different week number.")

                return week_number

        # Prepare initial data for the form
        initial_data = {'week_number': schedule.week_number}
        days_mapping = schedule.days or {}

        for i in range(1, 8):
            day_id = days_mapping.get(str(i))
            if day_id:
                try:
                    day = DailyMealPlan.objects.get(id=day_id)
                    initial_data[f'day_{i}'] = day
                except DailyMealPlan.DoesNotExist:
                    pass

        if request.method == 'POST':
            form = MealScheduleForm(request.POST, instance=schedule)
            if form.is_valid():
                # Check if week_number has changed and if there's already a schedule with that week number
                new_week_number = form.cleaned_data.get('week_number')
                if new_week_number != schedule.week_number:
                    # Check if there's another schedule with the same week number (excluding this one)
                    existing_schedule = MealSchedule.objects.filter(
                        meal_plan=meal_plan,
                        week_number=new_week_number,
                        user_meal_plan__isnull=True
                    ).exclude(id=schedule.id).first()

                    if existing_schedule:
                        form.add_error('week_number', f'A schedule for week {new_week_number} already exists.')
                        messages.error(request, f'A schedule for week {new_week_number} already exists.')
                        context = {
                            'form': form,
                            'schedule': schedule,
                            'meal_plan': meal_plan,
                            'title': f'Edit Meal Schedule: Week {schedule.week_number}',
                            'app_list': app_list,
                            **self.each_context(request),
                        }
                        return TemplateResponse(request, 'admin/meal_schedule_form.html', context)

                # Save the form
                form.save()

                # Save the days mapping
                days_mapping = {}
                for i in range(1, 8):
                    day = form.cleaned_data.get(f'day_{i}')
                    if day:
                        days_mapping[str(i)] = day.id

                schedule.days = days_mapping
                schedule.save()

                messages.success(request, f'Meal schedule for Week {schedule.week_number} updated successfully.')
                # Redirect to the appropriate page based on whether this is a user meal plan schedule or a global one
                if schedule.user_meal_plan:
                    return redirect('acfit_admin:edit-user-meal-plan', plan_id=schedule.user_meal_plan.id)
                else:
                    return redirect('acfit_admin:edit-meal-plan', plan_id=meal_plan.id)
        else:
            form = MealScheduleForm(instance=schedule, initial=initial_data)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'schedule': schedule,
            'meal_plan': meal_plan,
            'title': f'Edit Meal Schedule: Week {schedule.week_number}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/meal_schedule_form.html', context)

    def delete_meal_schedule_view(self, request, schedule_id):
        """View for deleting a meal schedule"""
        from meals.models import MealSchedule, DailyMealPlan
        from django.shortcuts import get_object_or_404

        schedule = get_object_or_404(MealSchedule, id=schedule_id)
        meal_plan = schedule.meal_plan

        if request.method == 'POST':
            schedule.delete()
            messages.success(request, f'Meal schedule for Week {schedule.week_number} deleted successfully.')
            return redirect('acfit_admin:edit-meal-plan', plan_id=meal_plan.id)

        # Prepare meal days info for the template
        meal_days_info = []

        # First try to use the days JSON field
        if schedule.days:
            for day_num, daily_plan_id in schedule.days.items():
                day_info = {'name': None}
                if daily_plan_id:
                    try:
                        daily_plan = DailyMealPlan.objects.get(id=daily_plan_id)
                        day_info['name'] = daily_plan.name
                    except DailyMealPlan.DoesNotExist:
                        pass
                meal_days_info.append((day_num, day_info))
            # Sort by day number
            meal_days_info.sort(key=lambda x: int(x[0]))

        # If days field is empty, fall back to weekday fields
        if not meal_days_info:
            weekday_fields = [
                ('1', {'name': schedule.monday.name if schedule.monday else None}),
                ('2', {'name': schedule.tuesday.name if schedule.tuesday else None}),
                ('3', {'name': schedule.wednesday.name if schedule.wednesday else None}),
                ('4', {'name': schedule.thursday.name if schedule.thursday else None}),
                ('5', {'name': schedule.friday.name if schedule.friday else None}),
                ('6', {'name': schedule.saturday.name if schedule.saturday else None}),
                ('7', {'name': schedule.sunday.name if schedule.sunday else None}),
            ]
            meal_days_info = weekday_fields

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'schedule': schedule,
            'meal_plan': meal_plan,
            'meal_days_info': meal_days_info,
            'title': f'Delete Meal Schedule: Week {schedule.week_number}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/meal_schedule_delete.html', context)

    # User Views
    def users_view(self, request):
        """View for listing all users"""
        from accounts.models import UserProfile

        users = UserProfile.objects.all().order_by('-user__date_joined')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'users': users,
            'title': 'Users',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/users.html', context)

    def add_user_view(self, request):
        """View for adding a new user"""
        from django.contrib.auth.forms import UserCreationForm
        from accounts.models import UserProfile

        if request.method == 'POST':
            form = UserCreationForm(request.POST)
            if form.is_valid():
                user = form.save()
                # Create a UserProfile for the new user
                UserProfile.objects.create(user=user)
                messages.success(request, 'User created successfully.')
                return redirect('admin:users')
        else:
            form = UserCreationForm()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Add User',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_form.html', context)

    def edit_user_view(self, request, user_id):
        """View for editing an existing user"""
        from django.contrib.auth import get_user_model
        from django.contrib.auth.forms import UserChangeForm
        from accounts.models import UserProfile
        from django.shortcuts import get_object_or_404

        User = get_user_model()
        user = get_object_or_404(User, id=user_id)
        user_profile = get_object_or_404(UserProfile, user=user)

        if request.method == 'POST':
            form = UserChangeForm(request.POST, instance=user)
            if form.is_valid():
                # Save user data
                form.save()

                # Save profile data
                if 'height' in request.POST and request.POST['height']:
                    user_profile.height = float(request.POST['height'])
                if 'weight' in request.POST and request.POST['weight']:
                    user_profile.weight = float(request.POST['weight'])
                if 'age' in request.POST and request.POST['age']:
                    user_profile.age = int(request.POST['age'])
                if 'gender' in request.POST:
                    user_profile.gender = request.POST['gender']
                if 'fitness_level' in request.POST:
                    user_profile.fitness_level = request.POST['fitness_level']
                if 'activity_level' in request.POST:
                    user_profile.activity_level = request.POST['activity_level']

                # Save the profile
                user_profile.save()

                messages.success(request, 'User updated successfully.')
                return redirect('acfit_admin:users')
        else:
            form = UserChangeForm(instance=user)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'user_obj': user,
            'user_profile': user_profile,
            'title': f'Edit User: {user.username}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_form.html', context)

    def delete_user_view(self, request, user_id):
        """View for deleting a user"""
        from django.contrib.auth import get_user_model
        from accounts.models import UserProfile
        from django.shortcuts import get_object_or_404
        from django.db import transaction

        User = get_user_model()
        user = get_object_or_404(User, id=user_id)

        if request.method == 'POST':
            # Delete the user and all related data in a transaction
            with transaction.atomic():
                # The user's profile and related data will be deleted via cascade
                user.delete()
                messages.success(request, f'User {user.username} deleted successfully.')
            return redirect('acfit_admin:users')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'user_obj': user,
            'title': f'Delete User: {user.username}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/user_delete.html', context)

    # FAQ Views
    def faqs_view(self, request):
        """View for listing all FAQs"""
        from support.models import FAQ
        from django.core.paginator import Paginator

        # Get filter parameters
        category = request.GET.get('category')
        is_active = request.GET.get('is_active')
        search_query = request.GET.get('q')

        # Base queryset
        queryset = FAQ.objects.all().order_by('order', 'created_at')

        # Apply filters
        if category:
            queryset = queryset.filter(category=category)
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)
        if search_query:
            queryset = queryset.filter(question__icontains=search_query) | queryset.filter(answer__icontains=search_query)

        # Get unique categories for filter dropdown
        categories = FAQ.objects.values_list('category', flat=True).distinct()
        categories = [c for c in categories if c]  # Remove empty categories

        # Pagination
        paginator = Paginator(queryset, 15)  # Show 15 FAQs per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'faqs': page_obj,
            'categories': categories,
            'selected_category': category,
            'is_active': is_active,
            'search_query': search_query,
            'is_paginated': paginator.num_pages > 1,
            'page_obj': page_obj,
            'title': 'Frequently Asked Questions',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/faqs.html', context)

    def add_faq_view(self, request):
        """View for adding a new FAQ"""
        from support.models import FAQ
        from django import forms

        class FAQForm(forms.ModelForm):
            class Meta:
                model = FAQ
                fields = ['question', 'answer', 'category', 'order', 'is_active']
                widgets = {
                    'question': forms.TextInput(attrs={'class': 'form-control'}),
                    'answer': forms.Textarea(attrs={'class': 'form-control', 'rows': 5}),
                    'category': forms.TextInput(attrs={'class': 'form-control'}),
                    'order': forms.NumberInput(attrs={'class': 'form-control'}),
                }

        if request.method == 'POST':
            form = FAQForm(request.POST)
            if form.is_valid():
                form.save()
                messages.success(request, 'FAQ added successfully.')
                return redirect('acfit_admin:faqs')
        else:
            # Get the highest order value and add 1 for the new FAQ
            highest_order = FAQ.objects.all().order_by('-order').values_list('order', flat=True).first() or 0
            form = FAQForm(initial={'order': highest_order + 1, 'is_active': True})

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'title': 'Add FAQ',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/faq_form.html', context)

    def edit_faq_view(self, request, faq_id):
        """View for editing an existing FAQ"""
        from support.models import FAQ
        from django import forms
        from django.shortcuts import get_object_or_404

        faq = get_object_or_404(FAQ, id=faq_id)

        class FAQForm(forms.ModelForm):
            class Meta:
                model = FAQ
                fields = ['question', 'answer', 'category', 'order', 'is_active']
                widgets = {
                    'question': forms.TextInput(attrs={'class': 'form-control'}),
                    'answer': forms.Textarea(attrs={'class': 'form-control', 'rows': 5}),
                    'category': forms.TextInput(attrs={'class': 'form-control'}),
                    'order': forms.NumberInput(attrs={'class': 'form-control'}),
                }

        if request.method == 'POST':
            form = FAQForm(request.POST, instance=faq)
            if form.is_valid():
                form.save()
                messages.success(request, 'FAQ updated successfully.')
                return redirect('acfit_admin:faqs')
        else:
            form = FAQForm(instance=faq)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'form': form,
            'faq': faq,
            'title': 'Edit FAQ',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/faq_form.html', context)

    def delete_faq_view(self, request, faq_id):
        """View for deleting an FAQ"""
        from support.models import FAQ
        from django.shortcuts import get_object_or_404

        faq = get_object_or_404(FAQ, id=faq_id)

        if request.method == 'POST':
            faq.delete()
            messages.success(request, 'FAQ deleted successfully.')
            return redirect('acfit_admin:faqs')

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'faq': faq,
            'title': 'Delete FAQ',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/faq_delete.html', context)

    # Feedback Views
    def feedback_view(self, request):
        """View for listing all user feedback"""
        from support.models import UserFeedback
        from django.core.paginator import Paginator

        # Get filter parameters
        feedback_type = request.GET.get('feedback_type')
        is_resolved = request.GET.get('is_resolved')
        search_query = request.GET.get('q')

        # Base queryset
        queryset = UserFeedback.objects.select_related('user').order_by('-created_at')

        # Apply filters
        if feedback_type:
            queryset = queryset.filter(feedback_type=feedback_type)
        if is_resolved == 'true':
            queryset = queryset.filter(is_resolved=True)
        elif is_resolved == 'false':
            queryset = queryset.filter(is_resolved=False)
        if search_query:
            queryset = queryset.filter(subject__icontains=search_query) | \
                      queryset.filter(message__icontains=search_query) | \
                      queryset.filter(user__username__icontains=search_query) | \
                      queryset.filter(user__email__icontains=search_query)

        # Pagination
        paginator = Paginator(queryset, 15)  # Show 15 feedback items per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get feedback types for filter dropdown
        from support.models import UserFeedback
        feedback_types = dict(UserFeedback.FEEDBACK_TYPES)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'feedback_items': page_obj,
            'feedback_types': feedback_types,
            'selected_type': feedback_type,
            'is_resolved': is_resolved,
            'search_query': search_query,
            'is_paginated': paginator.num_pages > 1,
            'page_obj': page_obj,
            'title': 'User Feedback',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/feedback.html', context)

    def view_feedback(self, request, feedback_id):
        """View for viewing a specific feedback item"""
        from support.models import UserFeedback
        from django.shortcuts import get_object_or_404

        feedback = get_object_or_404(UserFeedback.objects.select_related('user'), id=feedback_id)

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'feedback': feedback,
            'title': f'Feedback from {feedback.user.username}',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/feedback_detail.html', context)

    def resolve_feedback(self, request, feedback_id):
        """View for marking feedback as resolved/unresolved"""
        from support.models import UserFeedback
        from django.shortcuts import get_object_or_404

        feedback = get_object_or_404(UserFeedback, id=feedback_id)

        # Toggle the resolved status
        feedback.is_resolved = not feedback.is_resolved
        feedback.save()

        status_text = 'resolved' if feedback.is_resolved else 'unresolved'
        messages.success(request, f'Feedback marked as {status_text}.')

        # Redirect back to the feedback detail page
        return redirect('acfit_admin:view-feedback', feedback_id=feedback_id)

    def workout_calendar_view(self, request):
        """View for displaying workout days assigned to calendar dates"""
        from workouts.models import UserWorkoutPlan, WorkoutSchedule, WorkoutDay
        from accounts.models import UserProgress
        from django.contrib.auth import get_user_model
        from datetime import date, timedelta
        import calendar
        import logging

        logger = logging.getLogger(__name__)
        User = get_user_model()

        # Get filter parameters
        user_id = request.GET.get('user')
        plan_id = request.GET.get('plan')

        # Default to showing current month
        today = date.today()
        year = int(request.GET.get('year', today.year))
        month = int(request.GET.get('month', today.month))

        # Create calendar for the selected month
        cal = calendar.monthcalendar(year, month)
        month_start = date(year, month, 1)

        # Calculate next and previous months for navigation
        if month == 12:
            next_month = 1
            next_year = year + 1
        else:
            next_month = month + 1
            next_year = year

        if month == 1:
            prev_month = 12
            prev_year = year - 1
        else:
            prev_month = month - 1
            prev_year = year

        # Get active plans
        active_plans = UserWorkoutPlan.objects.filter(is_active=True).select_related('user__user', 'workout_plan')
        if user_id:
            active_plans = active_plans.filter(user__user_id=user_id)
        if plan_id:
            active_plans = active_plans.filter(id=plan_id)

        calendar_data = []

        for plan in active_plans:
            # Get user progress
            try:
                progress = UserProgress.objects.get(user=plan.user)
                current_program_day = progress.workout_day or 1
            except UserProgress.DoesNotExist:
                current_program_day = 1

            # Calculate days in the plan
            plan_days = {}

            # For each day in the month
            for week in cal:
                for day_num in week:
                    if day_num == 0:  # Day outside the month
                        continue

                    current_date = date(year, month, day_num)

                    # Skip dates before plan starts
                    if plan.start_date and current_date < plan.start_date:
                        continue

                    # Calculate program day
                    if plan.start_date:
                        days_from_plan_start = (current_date - plan.start_date).days
                        program_day = days_from_plan_start + 1

                        # Calculate week number and day in week
                        week_number = ((program_day - 1) // 7) + 1
                        day_in_week = ((program_day - 1) % 7) + 1

                        # Handle week looping if beyond plan duration
                        total_weeks = plan.workout_plan.duration_weeks or 4
                        if week_number > total_weeks:
                            is_looped = True
                            week_number = ((week_number - 1) % total_weeks) + 1
                        else:
                            is_looped = False

                        # Get workout day if available
                        workout_info = {
                            'date': current_date,
                            'program_day': program_day,
                            'week_number': week_number,
                            'day_in_week': day_in_week,
                            'is_looped': is_looped,
                            'is_today': current_date == today,
                            'is_current_program_day': program_day == current_program_day
                        }

                        try:
                            # Get schedule for the calculated week
                            schedule = WorkoutSchedule.objects.filter(
                                user_workout_plan=plan,
                                week_number=week_number
                            ).first()

                            if not schedule and is_looped:
                                # If looped and no user-specific schedule, try to get the template schedule
                                schedule = WorkoutSchedule.objects.filter(
                                    workout_plan=plan.workout_plan,
                                    week_number=week_number,
                                    user_workout_plan__isnull=True
                                ).first()

                            if schedule:
                                if schedule.days and str(day_in_week) in schedule.days:
                                    workout_day_id = schedule.days.get(str(day_in_week))
                                    if workout_day_id:
                                        try:
                                            workout_day = WorkoutDay.objects.get(id=workout_day_id)
                                            workout_info['workout_day'] = workout_day
                                            workout_info['is_rest_day'] = False
                                        except WorkoutDay.DoesNotExist:
                                            workout_info['workout_day'] = None
                                            workout_info['is_rest_day'] = True
                                            workout_info['note'] = "Workout day not found"
                                    else:
                                        workout_info['workout_day'] = None
                                        workout_info['is_rest_day'] = True
                                        workout_info['note'] = "Rest day (explicit null)"
                                else:
                                    # Try legacy weekday fields
                                    weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                                    if 1 <= day_in_week <= 7:
                                        field_name = weekday_fields[day_in_week - 1]
                                        workout_day = getattr(schedule, field_name)
                                        if workout_day:
                                            workout_info['workout_day'] = workout_day
                                            workout_info['is_rest_day'] = False
                                        else:
                                            workout_info['workout_day'] = None
                                            workout_info['is_rest_day'] = True
                                            workout_info['note'] = "Rest day (weekday field)"
                                    else:
                                        workout_info['workout_day'] = None
                                        workout_info['is_rest_day'] = True
                                        workout_info['note'] = "Invalid day in week"
                            else:
                                workout_info['workout_day'] = None
                                workout_info['is_rest_day'] = True
                                workout_info['note'] = "No schedule for week " + str(week_number)
                        except Exception as e:
                            logger.error(f"Error getting workout day: {str(e)}")
                            workout_info['workout_day'] = None
                            workout_info['is_rest_day'] = True
                            workout_info['note'] = f"Error: {str(e)}"

                        plan_days[current_date] = workout_info

            calendar_data.append({
                'plan': plan,
                'user': plan.user,
                'current_program_day': current_program_day,
                'days': plan_days,
                'start_date': plan.start_date,
                'start_date_formatted': plan.start_date.strftime("%b %d, %Y") if plan.start_date else "N/A"
            })

        # Get all users for filter dropdown
        users = User.objects.all().order_by('username')

        # Get all workout plans for filter dropdown
        workout_plans = UserWorkoutPlan.objects.filter(is_active=True).values('id', 'workout_plan__name', 'user__user__username').distinct()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'calendar_data': calendar_data,
            'calendar_weeks': cal,
            'month': month,
            'month_name': calendar.month_name[month],
            'year': year,
            'today': today,
            'prev_month': prev_month,
            'prev_year': prev_year,
            'next_month': next_month,
            'next_year': next_year,
            'users': users,
            'workout_plans': workout_plans,
            'selected_user': user_id,
            'selected_plan': plan_id,
            'title': f'Workout Calendar - {calendar.month_name[month]} {year}',
            'app_list': app_list,
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/workout_calendar.html', context)

    def program_day_view(self, request):
        """View for displaying program days in a sequential format"""
        from workouts.models import UserWorkoutPlan, WorkoutSchedule, WorkoutDay
        from accounts.models import UserProgress
        from django.contrib.auth import get_user_model
        from datetime import date, timedelta
        import logging

        logger = logging.getLogger(__name__)
        User = get_user_model()

        # Get filter parameters
        user_id = request.GET.get('user')
        plan_id = request.GET.get('plan')

        # Default to showing current month
        today = date.today()

        # Get active plans
        active_plans = UserWorkoutPlan.objects.filter(is_active=True).select_related('user__user', 'workout_plan')
        if user_id:
            active_plans = active_plans.filter(user__user_id=user_id)
        if plan_id:
            active_plans = active_plans.filter(id=plan_id)

        calendar_data = []

        for plan in active_plans:
            # Get user progress
            try:
                progress = UserProgress.objects.get(user=plan.user)
                current_program_day = progress.workout_day or 1
            except UserProgress.DoesNotExist:
                current_program_day = 1

            # Calculate program days
            program_days = []

            # Get the total number of days to show (28 days = 4 weeks)
            total_days_to_show = 28

            # Calculate the start date for the program days view
            # If the plan has already started, use the plan's start date
            # Otherwise, use today's date
            if plan.start_date and plan.start_date <= today:
                view_start_date = plan.start_date
            else:
                view_start_date = today

            # For each day in the program
            for day_offset in range(total_days_to_show):
                # Calculate the date for this program day
                current_date = view_start_date + timedelta(days=day_offset)

                # Calculate program day number (1-indexed)
                program_day = day_offset + 1

                # Calculate week number and day in week (both 1-indexed)
                week_number = ((program_day - 1) // 7) + 1
                day_in_week = ((program_day - 1) % 7) + 1

                # Handle week looping if beyond plan duration
                total_weeks = plan.workout_plan.duration_weeks or 4
                if week_number > total_weeks:
                    is_looped = True
                    original_week = week_number
                    week_number = ((week_number - 1) % total_weeks) + 1
                    loop_count = (original_week - 1) // total_weeks + 1
                else:
                    is_looped = False
                    loop_count = 1

                # Get workout day if available
                workout_info = {
                    'date': current_date,
                    'program_day': program_day,
                    'week_number': week_number,
                    'day_in_week': day_in_week,
                    'is_looped': is_looped,
                    'loop_count': loop_count if is_looped else None,
                    'is_today': current_date == today,
                    'is_current_program_day': program_day == current_program_day
                }

                try:
                    # Get schedule for the calculated week
                    schedule = WorkoutSchedule.objects.filter(
                        user_workout_plan=plan,
                        week_number=week_number
                    ).first()

                    if not schedule and is_looped:
                        # If looped and no user-specific schedule, try to get the template schedule
                        schedule = WorkoutSchedule.objects.filter(
                            workout_plan=plan.workout_plan,
                            week_number=week_number,
                            user_workout_plan__isnull=True
                        ).first()

                    if schedule:
                        # First try to get the day from the days JSON field
                        workout_day = None
                        if schedule.days and str(day_in_week) in schedule.days:
                            workout_day_id = schedule.days.get(str(day_in_week))
                            if workout_day_id:
                                try:
                                    workout_day = WorkoutDay.objects.get(id=workout_day_id)
                                except WorkoutDay.DoesNotExist:
                                    workout_day = None

                        # If not found in days JSON, try legacy weekday fields
                        if not workout_day:
                            weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                            if 1 <= day_in_week <= 7:
                                field_name = weekday_fields[day_in_week - 1]
                                workout_day = getattr(schedule, field_name)

                        if workout_day:
                            workout_info['workout_day'] = workout_day
                            workout_info['is_rest_day'] = False
                        else:
                            workout_info['workout_day'] = None
                            workout_info['is_rest_day'] = True
                            workout_info['note'] = "Rest day"
                    else:
                        workout_info['workout_day'] = None
                        workout_info['is_rest_day'] = True
                        workout_info['note'] = f"No schedule for week {week_number}"
                except Exception as e:
                    logger.error(f"Error getting workout day: {str(e)}")
                    workout_info['workout_day'] = None
                    workout_info['is_rest_day'] = True
                    workout_info['note'] = f"Error: {str(e)}"

                program_days.append(workout_info)

            # Calculate if the user has completed one cycle
            total_weeks = plan.workout_plan.duration_weeks or 4
            total_days_in_cycle = total_weeks * 7
            completed_one_cycle = current_program_day > total_days_in_cycle

            # Prefetch related workout day data for better performance
            for day_info in program_days:
                if day_info.get('workout_day'):
                    # Prefetch sessions, sections, and exercises
                    workout_day = day_info['workout_day']
                    if not hasattr(workout_day, '_prefetched_sessions'):
                        from django.db.models import Prefetch
                        from workouts.models import WorkoutSession, WorkoutSection, ExerciseInstance

                        # Prefetch sessions with their sections and exercises
                        sessions = WorkoutSession.objects.filter(workout_day=workout_day).prefetch_related(
                            Prefetch('sections', queryset=WorkoutSection.objects.prefetch_related(
                                Prefetch('exercises', queryset=ExerciseInstance.objects.select_related('exercise'))
                            ))
                        ).order_by('order')

                        # Attach the prefetched sessions to the workout day
                        setattr(workout_day, '_prefetched_sessions', sessions)
                        setattr(workout_day, 'sessions', sessions)

            calendar_data.append({
                'plan': plan,
                'user': plan.user,
                'current_program_day': current_program_day,
                'program_days': program_days,
                'start_date': plan.start_date,
                'start_date_formatted': plan.start_date.strftime("%b %d, %Y") if plan.start_date else "N/A",
                'completed_one_cycle': completed_one_cycle
            })

        # Get all users for filter dropdown
        users = User.objects.all().order_by('username')

        # Get all workout plans for filter dropdown
        workout_plans = UserWorkoutPlan.objects.filter(is_active=True).values('id', 'workout_plan__name', 'user__user__username').distinct()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'calendar_data': calendar_data,
            'today': today,
            'users': users,
            'workout_plans': workout_plans,
            'selected_user': user_id,
            'selected_plan': plan_id,
            'title': 'Program Days View',
            'app_list': app_list,
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/program_day_view_fixed.html', context)

    def meal_calendar_view(self, request):
        """View for displaying meal days assigned to calendar dates"""
        from meals.models import UserMealPlan, MealSchedule, DailyMealPlan
        from accounts.models import UserProgress
        from django.contrib.auth import get_user_model
        from datetime import date, timedelta
        import calendar as cal_module
        import logging

        logger = logging.getLogger(__name__)
        User = get_user_model()

        # Get filter parameters
        user_id = request.GET.get('user')
        plan_id = request.GET.get('plan')

        # Default to showing current month
        today = date.today()
        year = int(request.GET.get('year', today.year))
        month = int(request.GET.get('month', today.month))

        # Create calendar for the selected month
        cal = cal_module.monthcalendar(year, month)
        month_start = date(year, month, 1)
        month_end = date(year, month, cal_module.monthrange(year, month)[1])

        # Calculate next and previous months for navigation
        if month == 12:
            next_month = 1
            next_year = year + 1
        else:
            next_month = month + 1
            next_year = year

        if month == 1:
            prev_month = 12
            prev_year = year - 1
        else:
            prev_month = month - 1
            prev_year = year

        # Get active plans
        active_plans = UserMealPlan.objects.filter(is_active=True).select_related('user__user', 'meal_plan')
        if user_id:
            active_plans = active_plans.filter(user__user_id=user_id)
        if plan_id:
            active_plans = active_plans.filter(id=plan_id)

        calendar_data = []

        for plan in active_plans:
            # Get user progress
            try:
                progress = UserProgress.objects.get(user=plan.user)
                current_program_day = progress.meal_day or 1
            except UserProgress.DoesNotExist:
                current_program_day = 1

            # Calculate days in the plan
            plan_days = {}

            # Check if plan starts in the future
            plan_starts_in_future = plan.start_date > month_end

            # Check if plan starts in this month
            plan_starts_this_month = (plan.start_date.year == year and
                                     plan.start_date.month == month)

            # If plan hasn't started yet in this month view, skip it
            if plan_starts_in_future:
                continue

            # For each day in the month
            for week_idx, week in enumerate(cal):
                for day_idx, day_num in enumerate(week):
                    if day_num == 0:  # Day outside the month
                        continue

                    current_date = date(year, month, day_num)

                    # Skip dates before plan starts
                    if plan.start_date and current_date < plan.start_date:
                        # Add a placeholder to indicate this date is before the plan starts
                        plan_days[current_date] = {
                            'date': current_date,
                            'is_before_plan': True,
                            'is_today': current_date == today
                        }
                        continue

                    # Calculate program day
                    days_from_plan_start = (current_date - plan.start_date).days
                    program_day = days_from_plan_start + 1

                    # Calculate week number and day in week
                    week_number = ((program_day - 1) // 7) + 1
                    day_in_week = ((program_day - 1) % 7) + 1

                    # Handle week looping if beyond plan duration
                    total_weeks = plan.meal_plan.duration_weeks or 4
                    if week_number > total_weeks:
                        is_looped = True
                        original_week = week_number
                        week_number = ((week_number - 1) % total_weeks) + 1
                        loop_count = (original_week - 1) // total_weeks + 1
                    else:
                        is_looped = False
                        loop_count = 1

                    # Get meal day if available
                    meal_info = {
                        'date': current_date,
                        'program_day': program_day,
                        'week_number': week_number,
                        'day_in_week': day_in_week,
                        'is_looped': is_looped,
                        'loop_count': loop_count if is_looped else None,
                        'is_today': current_date == today,
                        'is_current_program_day': program_day == current_program_day,
                        'calendar_day': day_num,
                        'calendar_month': month,
                        'calendar_year': year
                    }

                    try:
                        # Get schedule for the calculated week
                        schedule = MealSchedule.objects.filter(
                            user_meal_plan=plan,
                            week_number=week_number
                        ).first()

                        if not schedule and is_looped:
                            # If looped and no user-specific schedule, try to get the template schedule
                            schedule = MealSchedule.objects.filter(
                                meal_plan=plan.meal_plan,
                                week_number=week_number,
                                user_meal_plan__isnull=True
                            ).first()

                        if schedule:
                            # First try to get the day from the days JSON field
                            meal_day = None
                            if schedule.days and str(day_in_week) in schedule.days:
                                meal_day_id = schedule.days.get(str(day_in_week))
                                if meal_day_id:
                                    try:
                                        meal_day = DailyMealPlan.objects.get(id=meal_day_id)
                                    except DailyMealPlan.DoesNotExist:
                                        meal_day = None

                            # If not found in days JSON, try legacy weekday fields
                            if not meal_day:
                                weekday_fields = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                                if 1 <= day_in_week <= 7:
                                    field_name = weekday_fields[day_in_week - 1]
                                    meal_day = getattr(schedule, field_name)

                            if meal_day:
                                meal_info['meal_day'] = meal_day
                                meal_info['is_rest_day'] = False
                                meal_info['meal_name'] = meal_day.name
                                meal_info['url'] = reverse('admin:meals_dailymealplan_change', args=[meal_day.id])
                            else:
                                meal_info['meal_day'] = None
                                meal_info['is_rest_day'] = True
                                meal_info['note'] = "No meals assigned"
                        else:
                            meal_info['meal_day'] = None
                            meal_info['is_rest_day'] = True
                            meal_info['note'] = f"No schedule for week {week_number}"
                    except Exception as e:
                        logger.error(f"Error getting meal day: {str(e)}")
                        meal_info['meal_day'] = None
                        meal_info['is_rest_day'] = True
                        meal_info['note'] = f"Error: {str(e)}"

                    plan_days[current_date] = meal_info

            # Only add plans that have days in this month
            if plan_days:
                calendar_data.append({
                    'plan': plan,
                    'user': plan.user,
                    'current_program_day': current_program_day,
                    'days': plan_days,
                    'start_date': plan.start_date,
                    'start_date_formatted': plan.start_date.strftime("%b %d, %Y"),
                    'plan_starts_this_month': plan_starts_this_month
                })

        # Get all users for filter dropdown
        users = User.objects.all().order_by('username')

        # Get all meal plans for filter dropdown
        meal_plans = UserMealPlan.objects.filter(is_active=True).values('id', 'meal_plan__name', 'user__user__username').distinct()

        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'calendar_data': calendar_data,
            'calendar_weeks': cal,
            'month': month,
            'month_name': cal_module.month_name[month],
            'year': year,
            'today': today,
            'prev_month': prev_month,
            'prev_year': prev_year,
            'next_month': next_month,
            'next_year': next_year,
            'users': users,
            'meal_plans': meal_plans,
            'selected_user': user_id,
            'selected_plan': plan_id,
            'title': f'Meal Calendar - {cal_module.month_name[month]} {year}',
            'app_list': app_list,
            **self.each_context(request),
        }

        return TemplateResponse(request, 'admin/meal_calendar.html', context)

    def documentation_view(self, request):
        """View for displaying comprehensive admin documentation"""
        # Add app_list for compatibility with admin templates
        app_list = self.get_app_list(request)

        context = {
            'title': 'AC-FIT Admin Documentation',
            'app_list': app_list,
            **self.each_context(request),
        }
        return TemplateResponse(request, 'admin/documentation.html', context)

# Create the custom admin site instance
acfit_admin_site = ACFitAdminSite(name='acfit_admin')

# Register models with the custom admin site
# We'll register models in a separate function to avoid circular imports

def register_models():
    """Register models from other apps with the custom admin site"""
    try:
        # Import here to avoid circular imports
        from django.contrib.auth import get_user_model
        from django.contrib.auth.models import Group, Permission
        from django.contrib.auth.admin import UserAdmin

        # Register auth models
        User = get_user_model()
        acfit_admin_site.register(User, UserAdmin)
        acfit_admin_site.register(Group)
        acfit_admin_site.register(Permission)

        # Register accounts models
        from accounts.models import UserProfile, UserProgress, UserScore
        acfit_admin_site.register(UserProfile, BetterUserProfileAdmin)
        acfit_admin_site.register(UserProgress, BetterUserProgressAdmin)
        acfit_admin_site.register(UserScore, BetterUserScoreAdmin)

        # Register workouts models
        from workouts.models import WorkoutPlan, WorkoutDay, UserWorkoutPlan, Exercise, WorkoutSection, ExerciseInstance, WorkoutSessionLog, WorkoutSession, WorkoutVideo
        from workouts.admin import WorkoutSessionLogAdmin, WorkoutVideoAdmin
        acfit_admin_site.register(WorkoutPlan, BetterWorkoutPlanAdmin)
        acfit_admin_site.register(WorkoutDay, BetterWorkoutDayAdmin)
        acfit_admin_site.register(UserWorkoutPlan, BetterUserWorkoutPlanAdmin)
        acfit_admin_site.register(Exercise, BetterExerciseAdmin)
        acfit_admin_site.register(WorkoutSection, BetterModelAdmin)
        acfit_admin_site.register(ExerciseInstance, BetterModelAdmin)
        acfit_admin_site.register(WorkoutSession, BetterModelAdmin)
        acfit_admin_site.register(WorkoutSessionLog, WorkoutSessionLogAdmin)
        acfit_admin_site.register(WorkoutVideo, WorkoutVideoAdmin)

        # Register meals models
        from meals.models import MealPlan, UserMealPlan, DailyMealPlan, Meal
        acfit_admin_site.register(MealPlan, BetterMealPlanAdmin)
        acfit_admin_site.register(UserMealPlan, BetterUserMealPlanAdmin)
        acfit_admin_site.register(DailyMealPlan, BetterDailyMealPlanAdmin)
        acfit_admin_site.register(Meal, BetterMealAdmin)

        # Register support models
        from support.models import FAQ, UserFeedback
        from support.admin import FAQAdmin, UserFeedbackAdmin
        acfit_admin_site.register(FAQ, FAQAdmin)
        acfit_admin_site.register(UserFeedback, UserFeedbackAdmin)

        print("Successfully registered all models with the custom admin site.")
    except Exception as e:
        print(f"Error registering models: {e}")

# Register admin_site models
class QuestionnaireQuestionAdmin(admin.ModelAdmin):
    list_display = ('text', 'field_name', 'question_type', 'required', 'order', 'plan_assignment_status')
    list_filter = ('question_type', 'required', 'affects_workout_plans', 'affects_meal_plans')
    search_fields = ('text', 'field_name')
    ordering = ('order',)

    def plan_assignment_status(self, obj):
        """Display plan assignment status in the admin list"""
        statuses = []
        if obj.affects_workout_plans:
            statuses.append('Workout Plans')
        if obj.affects_meal_plans:
            statuses.append('Meal Plans')
        if not statuses:
            return 'Not used for plans'
        return ', '.join(statuses)
    plan_assignment_status.short_description = 'Plan Assignment'

class QuestionnaireOptionAdmin(admin.ModelAdmin):
    list_display = ('text', 'value', 'question', 'order')
    list_filter = ('question',)
    search_fields = ('text', 'value')
    ordering = ('question', 'order')

# Custom ModelAdmin class that handles MultiSelectField properly
class BetterModelAdmin(admin.ModelAdmin):
    def __init__(self, model, admin_site):
        super().__init__(model, admin_site)
        # Find all MultiSelectField fields in the model
        self.multiselect_fields = []
        for field in model._meta.fields:
            if isinstance(field, MultiSelectField):
                self.multiselect_fields.append(field.name)

        # For each MultiSelectField, create a display method
        for field_name in self.multiselect_fields:
            method_name = f'{field_name}_display'
            if not hasattr(self, method_name):
                # Use a closure to capture the field_name properly
                def make_display_method(field):
                    return lambda obj: self.display_multiselect_value(obj, field)

                display_method = make_display_method(field_name)
                display_method.short_description = model._meta.get_field(field_name).verbose_name
                setattr(self, method_name, display_method)

        # Add the display methods to readonly_fields if they're not already there
        if hasattr(self, 'readonly_fields') and self.readonly_fields:
            readonly_fields = list(self.readonly_fields)
            for field_name in self.multiselect_fields:
                method_name = f'{field_name}_display'
                if method_name not in readonly_fields:
                    readonly_fields.append(method_name)
            self.readonly_fields = tuple(readonly_fields)

    def get_list_display(self, request):
        """Add display methods for MultiSelectFields to list_display"""
        list_display = list(super().get_list_display(request))

        # For each MultiSelectField in list_display, replace it with its display method
        for i, field_name in enumerate(list_display):
            if field_name in self.multiselect_fields:
                display_method = f'{field_name}_display'
                if hasattr(self, display_method):
                    list_display[i] = display_method

        return list_display

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        # Check if the field is a MultiSelectField
        if isinstance(db_field, MultiSelectField):
            # Use our custom widget
            kwargs['widget'] = CustomMultiSelectWidget(choices=db_field.choices)
        return super().formfield_for_dbfield(db_field, request, **kwargs)

    def display_multiselect_value(self, obj, field_name):
        """Helper method to properly display MultiSelectField values"""
        try:
            field = obj._meta.get_field(field_name)
            if not isinstance(field, MultiSelectField):
                return getattr(obj, field_name)

            value = getattr(obj, field_name)
            if not value:
                return mark_safe('<span class="empty-value">-</span>')

            # Use our custom display widget
            widget = CustomMultiSelectDisplayWidget(choices=field.choices)
            return widget.render(field_name, value)
        except Exception as e:
            return mark_safe(f'<span class="error-value">Error displaying {field_name}: {str(e)}</span>')

# Move this before register_models is defined
from accounts.admin import UserProfileAdmin, UserProgressAdmin, UserScoreAdmin
from workouts.admin import WorkoutPlanAdmin, WorkoutDayAdmin, UserWorkoutPlanAdmin, ExerciseAdmin
from meals.admin import MealPlanAdmin, UserMealPlanAdmin, DailyMealPlanAdmin, MealAdmin

# Create better versions of all admin classes
class BetterUserProfileAdmin(BetterModelAdmin, UserProfileAdmin):
    # The automatic MultiSelectField handling in BetterModelAdmin will take care of health_conditions
    pass

class BetterUserProgressAdmin(BetterModelAdmin, UserProgressAdmin):
    pass

class BetterUserScoreAdmin(BetterModelAdmin, UserScoreAdmin):
    pass

class BetterWorkoutPlanAdmin(BetterModelAdmin, WorkoutPlanAdmin):
    # The automatic MultiSelectField handling in BetterModelAdmin will take care of health_conditions_allowed
    fieldsets = WorkoutPlanAdmin.fieldsets

class BetterWorkoutDayAdmin(BetterModelAdmin, WorkoutDayAdmin):
    pass

class BetterUserWorkoutPlanAdmin(BetterModelAdmin, UserWorkoutPlanAdmin):
    pass

class BetterExerciseAdmin(BetterModelAdmin, ExerciseAdmin):
    pass

class BetterMealPlanAdmin(BetterModelAdmin, MealPlanAdmin):
    # The automatic MultiSelectField handling in BetterModelAdmin will take care of health_conditions_allowed
    fieldsets = MealPlanAdmin.fieldsets

class BetterUserMealPlanAdmin(BetterModelAdmin, UserMealPlanAdmin):
    pass

class BetterDailyMealPlanAdmin(BetterModelAdmin, DailyMealPlanAdmin):
    pass

class BetterMealAdmin(BetterModelAdmin, MealAdmin):
    pass

# Update the existing admin classes to inherit from BetterModelAdmin
class BetterQuestionnaireQuestionAdmin(BetterModelAdmin, QuestionnaireQuestionAdmin):
    pass

class BetterQuestionnaireOptionAdmin(BetterModelAdmin, QuestionnaireOptionAdmin):
    pass

# Register with the better admin classes
acfit_admin_site.register(QuestionnaireQuestion, BetterQuestionnaireQuestionAdmin)
acfit_admin_site.register(QuestionnaireOption, BetterQuestionnaireOptionAdmin)

# Call register_models when the module is loaded
register_models()
