import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'dart:developer' as developer;
import 'dart:math'; // For random IDs

class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Unique channel details
  static const AndroidNotificationChannel _mealChannel = AndroidNotificationChannel(
    'meal_reminders', // id
    'Meal Reminders', // title
    description: 'Notifications to remind you about upcoming meals.', // description
    importance: Importance.max,
    playSound: true,
    sound: RawResourceAndroidNotificationSound('notification'), // Example: if you have notification.mp3 in res/raw
  );
  static const AndroidNotificationChannel _workoutChannel = AndroidNotificationChannel(
    'workout_reminders', // id
    'Workout Reminders', // title
    description: 'Notifications to remind you about upcoming workouts.', // description
    importance: Importance.max,
    playSound: true,
     sound: RawResourceAndroidNotificationSound('notification'),
  );
  static const AndroidNotificationChannel _hydrationChannel = AndroidNotificationChannel(
    'hydration_reminders', // id
    'Hydration Reminders', // title
    description: 'Notifications to remind you to drink water.', // description
    importance: Importance.defaultImportance, // Lower importance for hydration
    playSound: true,
    enableVibration: false,
     sound: RawResourceAndroidNotificationSound('notification'),
  );

  static Future<void> initialize() async {
    try {
      tz.initializeTimeZones();
      // Use a specific location like London if needed, otherwise rely on local timezone
      // Example: tz.setLocalLocation(tz.getLocation('Europe/London'));

      // Setup for Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher'); // Ensure you have this icon

      // Setup for iOS (requesting permissions happens separately)
      const DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings(
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
          // onDidReceiveLocalNotification: onDidReceiveLocalNotification // Optional callback
          );

      const InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Create Android Notification Channels
      final androidImplementation = _notificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      await androidImplementation?.createNotificationChannel(_mealChannel);
      await androidImplementation?.createNotificationChannel(_workoutChannel);
      await androidImplementation?.createNotificationChannel(_hydrationChannel);

      await _notificationsPlugin.initialize(
        initializationSettings,
        // onDidReceiveNotificationResponse: onDidReceiveNotificationResponse // Optional callback for notification tap
      );
      developer.log('LocalNotificationService Initialized', name: 'LocalNotificationService');
    } catch (e) {
      developer.log('Error initializing LocalNotificationService: $e', name: 'LocalNotificationService', error: e);
    }
  }

  static Future<bool> requestPermissions() async {
    try {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      bool? androidPermissionGranted = false;
      if (androidImplementation != null) {
         androidPermissionGranted = await androidImplementation.requestNotificationsPermission();
      }

      // Request iOS permissions directly through the main plugin
      bool? iosPermissionGranted = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );

      developer.log('Notification Permissions: Android=$androidPermissionGranted, iOS=$iosPermissionGranted', name: 'LocalNotificationService');
      return (androidPermissionGranted ?? false) || (iosPermissionGranted ?? false);
    } catch (e) {
       developer.log('Error requesting notification permissions: $e', name: 'LocalNotificationService', error: e);
       return false;
    }

  }

  // --- Scheduling Methods ---

  static Future<void> scheduleMealReminder({required int mealLogId, required String mealName, required DateTime scheduledTime}) async {
    // Use a specific ID range for meals, maybe based on mealLogId if unique enough and within integer range
    // Or hash mealLogId/mealName + date to get a somewhat stable ID
    // Simplification: Use mealLogId if it's small enough, otherwise use a random ID in range
    int notificationId = 2000 + (mealLogId % 1000); // Example: Map mealLogId into 2000-2999 range

    // Schedule 15 minutes before
    final tz.TZDateTime notificationTime = _scheduleDateTime(scheduledTime.subtract(const Duration(minutes: 15)));

    // Check if the notification time is in the far future (meaning it was in the past)
    if (notificationTime.isAfter(tz.TZDateTime.now(tz.local).add(const Duration(days: 365)))) {
       developer.log('Skipping past meal reminder for: $mealName at $scheduledTime', name: 'LocalNotificationService');
      return; // Don't schedule reminders for past meals
    }

    developer.log('Scheduling meal reminder ID $notificationId for $mealName at $notificationTime', name: 'LocalNotificationService');

     try {
        await _notificationsPlugin.zonedSchedule(
          notificationId,
          '🍽️ Meal Time!',
          'Ready for your ${mealName.toLowerCase()}? Time to eat!',
          notificationTime,
          NotificationDetails(
            android: AndroidNotificationDetails(
              _mealChannel.id,
              _mealChannel.name,
              channelDescription: _mealChannel.description,
              importance: _mealChannel.importance,
              priority: Priority.high,
              // groupKey: 'meal_group',
            ),
            iOS: const DarwinNotificationDetails(
              sound: 'default', // Use default iOS sound
              presentAlert: true,
              presentBadge: true, // Increment badge for meals
              presentSound: true,
             // threadIdentifier: 'meal_reminders', // Group notifications on iOS
            ),
          ),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        );
     } catch (e) {
       developer.log('Error scheduling meal reminder ID $notificationId for $mealName: $e', name: 'LocalNotificationService', error: e);
     }

  }

  static Future<void> scheduleWorkoutReminder({required int workoutLogId, required String workoutName, required DateTime scheduledTime}) async {
    // Use a specific ID range for workouts
    int notificationId = 3000 + (workoutLogId % 1000);

    // Schedule 30 minutes before
    final tz.TZDateTime notificationTime = _scheduleDateTime(scheduledTime.subtract(const Duration(minutes: 30)));

    // Check if the notification time is in the far future (meaning it was in the past)
    if (notificationTime.isAfter(tz.TZDateTime.now(tz.local).add(const Duration(days: 365)))) {
       developer.log('Skipping past workout reminder for: $workoutName at $scheduledTime', name: 'LocalNotificationService');
      return; // Don't schedule reminders for past workouts
    }

    developer.log('Scheduling workout reminder ID $notificationId for $workoutName at $notificationTime', name: 'LocalNotificationService');

    try {
        await _notificationsPlugin.zonedSchedule(
          notificationId,
          '💪 Workout Time!',
          'Get ready to sweat! Your workout "$workoutName" starts in 30 minutes.',
          notificationTime,
          NotificationDetails(
            android: AndroidNotificationDetails(
              _workoutChannel.id,
              _workoutChannel.name,
              channelDescription: _workoutChannel.description,
              importance: _workoutChannel.importance,
              priority: Priority.high,
              // groupKey: 'workout_group',
            ),
             iOS: const DarwinNotificationDetails(
              sound: 'default',
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
              // threadIdentifier: 'workout_reminders',
            ),
          ),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        );
    } catch (e) {
      developer.log('Error scheduling workout reminder ID $notificationId for $workoutName: $e', name: 'LocalNotificationService', error: e);
    }
  }

  static Future<void> scheduleHydrationReminders() async {
    await cancelHydrationReminders(); // Cancel previous hydration reminders first
    developer.log('Scheduling hydration reminders', name: 'LocalNotificationService');

    final Random random = Random();
    const String groupKey = 'hydration_group'; // Group hydration notifications

    // Define start and end times for reminders
    const int startHour = 8;
    const int endHour = 22; // Up to 10 PM
    const int intervalHours = 2;

    for (int hour = startHour; hour <= endHour; hour += intervalHours) {
      // Generate a unique ID for each time slot
      // Use a base ID plus the hour to ensure uniqueness within the loop
      final int notificationId = 1000 + hour;

      // Calculate the next instance of this time (e.g., next 8 AM, next 10 AM)
      final tz.TZDateTime scheduledTime = _nextInstanceOfTime(hour, 0); // Schedule at the top of the hour

      developer.log('Scheduling hydration reminder ID $notificationId for $scheduledTime', name: 'LocalNotificationService');

      try {
        await _notificationsPlugin.zonedSchedule(
          notificationId,
          '💧 Stay Hydrated!',
          'Time for a glass of water to keep energy levels up!',
          scheduledTime,
          NotificationDetails(
            android: AndroidNotificationDetails(
              _hydrationChannel.id,
              _hydrationChannel.name,
              channelDescription: _hydrationChannel.description,
              importance: _hydrationChannel.importance,
              priority: Priority.defaultPriority,
              groupKey: groupKey,
              // styleInformation: BigTextStyleInformation(''), // Optional: For longer text
            ),
            iOS: const DarwinNotificationDetails(
              sound: 'default', // Use default iOS sound
              presentAlert: true,
              presentBadge: false, // Don't increment badge for hydration
              presentSound: true,
              // threadIdentifier: groupKey, // Group notifications on iOS
            ),
          ),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          matchDateTimeComponents: DateTimeComponents.time, // Match time daily
        );
      } catch (e) {
        developer.log('Error scheduling hydration reminder ID $notificationId: $e', name: 'LocalNotificationService', error: e);
      }
    }
     developer.log('Finished scheduling hydration reminders', name: 'LocalNotificationService');
  }

  // --- Cancellation Methods ---

  static Future<void> cancelMealReminders() async {
    // Find notifications with meal channel ID and cancel them
    developer.log('Cancelling all meal reminders', name: 'LocalNotificationService');
    // Refine later: Cancel only notifications with meal channel/IDs if needed
    // For now, cancel all notifications associated with the meal channel ID *conceptually*
    // Since we don't have per-ID cancellation yet, cancel all is the fallback.
    // await _notificationsPlugin.cancelAll();
     final List<PendingNotificationRequest> pendingNotifications =
        await _notificationsPlugin.pendingNotificationRequests();
    for (PendingNotificationRequest req in pendingNotifications) {
      // Check if ID is in the typical range for meals (e.g., 2000-2999)
      if (req.id >= 2000 && req.id < 3000) {
         developer.log('Cancelling Meal Reminder ID: ${req.id}', name: 'LocalNotificationService');
        await _notificationsPlugin.cancel(req.id);
      }
    }
  }

  static Future<void> cancelWorkoutReminders() async {
    developer.log('Cancelling all workout reminders', name: 'LocalNotificationService');
    // Refine later: Cancel only notifications with workout channel/IDs if needed
     final List<PendingNotificationRequest> pendingNotifications =
        await _notificationsPlugin.pendingNotificationRequests();
    for (PendingNotificationRequest req in pendingNotifications) {
      // Check if ID is in the typical range for workouts (e.g., 3000-3999)
      if (req.id >= 3000 && req.id < 4000) {
         developer.log('Cancelling Workout Reminder ID: ${req.id}', name: 'LocalNotificationService');
        await _notificationsPlugin.cancel(req.id);
      }
    }
  }

  static Future<void> cancelHydrationReminders() async {
    developer.log('Cancelling all hydration reminders', name: 'LocalNotificationService');
    // Cancel only notifications with hydration channel/IDs
    final List<PendingNotificationRequest> pendingNotifications =
        await _notificationsPlugin.pendingNotificationRequests();
    for (PendingNotificationRequest req in pendingNotifications) {
      // Check if ID is in the typical range for hydration (e.g., 1000-1999)
      if (req.id >= 1000 && req.id < 2000) {
        developer.log('Cancelling Hydration Reminder ID: ${req.id}', name: 'LocalNotificationService');
        await _notificationsPlugin.cancel(req.id);
      }
    }
  }

  static Future<void> cancelAllReminders() async {
    developer.log('Cancelling ALL local notifications', name: 'LocalNotificationService');
    await _notificationsPlugin.cancelAll();
  }

  // --- Helper Methods --- (For zonedSchedule)

  static tz.TZDateTime _nextInstanceOfTime(int hour, int minute) {
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    tz.TZDateTime scheduledDate = tz.TZDateTime(tz.local, now.year, now.month, now.day, hour, minute);
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
    return scheduledDate;
  }

   static tz.TZDateTime _scheduleDateTime(DateTime dateTime) {
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    tz.TZDateTime scheduledDate = tz.TZDateTime.from(dateTime, tz.local);

    // Ensure the scheduled date is in the future
    if (scheduledDate.isBefore(now)) {
      // If the exact time has passed today, don't schedule for the past
      // Or decide if it should schedule for the next day? For now, let's skip past times.
      developer.log('Attempted to schedule notification in the past: $scheduledDate. Skipping.', name: 'LocalNotificationService');
      // To schedule for the next occurrence (e.g., daily reminder):
      // scheduledDate = scheduledDate.add(Duration(days: 1));
      // However, for specific meal/workout times, scheduling for the past is usually not desired.
      // We might return a specific value or throw to indicate this.
      // For simplicity, let's return a marker time far in the future to effectively skip scheduling.
       return tz.TZDateTime.now(tz.local).add(const Duration(days: 365 * 10)); // Far future
    }
    return scheduledDate;
  }


  // --- Optional Callbacks ---

  // static void onDidReceiveNotificationResponse(NotificationResponse notificationResponse) async {
  //   final String? payload = notificationResponse.payload;
  //   if (notificationResponse.payload != null) {
  //     developer.log('Notification payload: $payload', name: 'LocalNotificationService');
  //     // Handle navigation based on payload here
  //   }
  // }

  // static void onDidReceiveLocalNotification(
  //     int id, String? title, String? body, String? payload) async {
  //   // display a dialog with the notification details, tap ok to go to another page
  //   developer.log('iOS FG Notification: $id, $title, $body, $payload', name: 'LocalNotificationService');
  // }
} 