import 'dart:async';
import '../models/faq_data.dart';
import '../services/api_service.dart';
import '../constants/api_constants.dart';
import '../utils/logger.dart';

class FAQService {
  /// Fetches all FAQs from the API, handling pagination
  static Future<List<FAQ>> getFAQs() async {
    List<FAQ> allFaqs = [];
    final apiService = ApiService();

    try {
      Logger.log('Fetching FAQs from API', tag: 'FAQService');
      final response = await apiService.get(ApiConstants.faqs);

      // Check if response is paginated
      if (response is Map<String, dynamic> &&
          response.containsKey('results') &&
          response['results'] is List) {
        final List<dynamic> results = response['results'];
        allFaqs.addAll(results.map((item) => FAQ.fromJson(item)).toList());
        Logger.log('Received ${allFaqs.length} FAQs from paginated response',
            tag: 'FAQService');
      } else if (response is List) {
        // Handle case where API returns a direct array
        allFaqs.addAll(
            (response).map((item) => FAQ.fromJson(item)).toList());
        Logger.log('Received ${allFaqs.length} FAQs from direct array response',
            tag: 'FAQService');
      } else {
        // If we get here, we have an unexpected response format
        Logger.log('Unexpected FAQ response format: $response',
            tag: 'FAQService');
        throw const FormatException('Unexpected API response format');
      }

      return allFaqs;
    } catch (e) {
      Logger.log('Error fetching FAQs: $e', tag: 'FAQService', error: e);
      // If no FAQs are available, return an empty list
      if (allFaqs.isEmpty) {
        Logger.log('No FAQs available from server', tag: 'FAQService');
        return [];
      }
      throw Exception('Failed to load FAQs: ${e.toString()}');
    }
  }
}
