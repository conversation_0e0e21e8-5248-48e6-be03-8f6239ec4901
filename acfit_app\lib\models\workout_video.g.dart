// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workout_video.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkoutVideo _$WorkoutVideoFromJson(Map<String, dynamic> json) => WorkoutVideo(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      description: json['description'] as String?,
      videoUrl: json['video_url'] as String?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      durationSeconds: (json['duration_seconds'] as num?)?.toInt(),
      order: (json['order'] as num).toInt(),
      createdAt: json['created_at'] as String?,
    );

Map<String, dynamic> _$WorkoutVideoToJson(WorkoutVideo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'video_url': instance.videoUrl,
      'thumbnail_url': instance.thumbnailUrl,
      'duration_seconds': instance.durationSeconds,
      'order': instance.order,
      'created_at': instance.createdAt,
    };
