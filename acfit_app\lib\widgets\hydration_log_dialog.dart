import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Required for TextInputFormatter
// import 'dart:developer' as developer; // Import developer for logging (removed)
import 'dart:math'; // For min/max in +/- buttons

class HydrationLogDialog extends StatefulWidget {
  final int currentAmountMl;
  final int targetAmountMl;

  const HydrationLogDialog({
    Key? key,
    required this.currentAmountMl,
    required this.targetAmountMl,
  }) : super(key: key);

  @override
  _HydrationLogDialogState createState() => _HydrationLogDialogState();
}

class _HydrationLogDialogState extends State<HydrationLogDialog> {
  late TextEditingController _mlController;
  int _amountToLog = 250; // Default to 250ml
  String _selectedPresetKey = ''; // Use key to track selected preset

  // --- Presets Refined ---
  // Using LinkedHashMap to preserve insertion order for display
  // More distinct presets with better icons
  final Map<String, ({int amount, IconData icon})> _presets = {
    'Glass': (amount: 250, icon: Icons.water_drop_outlined),
    'Bottle': (amount: 500, icon: Icons.local_drink_outlined),
    'Large Bottle': (amount: 750, icon: Icons.battery_5_bar_outlined), // Placeholder
    'Liter': (amount: 1000, icon: Icons.opacity), // Liter icon
    // 'Liter': (amount: 1000, icon: Icons.opacity), // Example, maybe too large?
  };

  @override
  void initState() {
    super.initState();
    // Initialize with the 'Glass' preset amount
    _amountToLog = _presets['Glass']!.amount;
    _selectedPresetKey = 'Glass'; // Mark 'Glass' as initially selected
    _mlController = TextEditingController(text: _amountToLog.toString());
    _mlController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _mlController.removeListener(_onTextChanged);
    _mlController.dispose();
    super.dispose();
  }

  // Update state when text field changes
  void _onTextChanged() {
    final text = _mlController.text;
    final amount = int.tryParse(text) ?? 0;

    // Only update state if the parsed amount is different
    if (amount != _amountToLog) {
      // Check if the new amount matches any preset
      String matchingPresetKey = '';
      _presets.forEach((key, value) {
        if (value.amount == amount) {
          matchingPresetKey = key;
        }
      });

      // Update state only if needed (amount or selection changed)
      if (amount != _amountToLog || matchingPresetKey != _selectedPresetKey) {
        setState(() {
          _amountToLog = amount;
          _selectedPresetKey =
              matchingPresetKey; // Update selection based on text field value
        });
      }
    }
  }

  // Update state when a preset button is tapped
  void _selectPreset(String key) {
    final amount = _presets[key]!.amount;
    setState(() {
      _amountToLog = amount;
      _selectedPresetKey = key; // Highlight this button
      // Update text field ONLY if the value actually changes
      if (_mlController.text != amount.toString()) {
        _mlController.text = amount.toString();
        // Move cursor to end
        _mlController.selection = TextSelection.fromPosition(
            TextPosition(offset: _mlController.text.length));
      }
    });
    // Removed log
  }

  // Increment/Decrement amount via buttons
  void _adjustAmount(int delta) {
    int currentAmount = int.tryParse(_mlController.text) ?? 0;
    int newAmount = max(0, currentAmount + delta); // Prevent negative values
    _mlController.text = newAmount.toString();
    // Trigger listener explicitly as direct text change might not fire it immediately
    _onTextChanged();
    // Move cursor to end
    _mlController.selection = TextSelection.fromPosition(
        TextPosition(offset: _mlController.text.length));
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    // Use primary color for consistency, but allow override if needed
    final Color accentColor = Colors.blue.shade700; // Use a specific blue

    return AlertDialog(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0)), // Consistent rounding
      backgroundColor:
          theme.dialogBackgroundColor, // Use theme dialog background
      titlePadding:
          const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 8.0), // Adjusted padding
      contentPadding: const EdgeInsets.symmetric(
          horizontal: 24.0, vertical: 16.0), // Adjusted padding
      actionsPadding:
          const EdgeInsets.fromLTRB(24.0, 8.0, 24.0, 20.0), // Adjusted padding

      title: Text(
        'Log Water Intake',
        textAlign: TextAlign.center,
        style: theme.dialogTheme.titleTextStyle ??
            theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontFamily: 'Work Sans',
              color: accentColor, // Use accent color for title
            ),
      ),

      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment:
              CrossAxisAlignment.stretch, // Stretch children horizontally
          children: <Widget>[
            // --- Current Progress Indicator (Optional Enhancement) ---
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.water_drop_outlined, color: Colors.blue, size: 18),
                  const SizedBox(width: 8),
                  Text(
                    'Today: ${widget.currentAmountMl} / ${widget.targetAmountMl} ml',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      fontFamily: 'Work Sans',
                    ),
                  ),
                ],
              ),
            ),
            // Goal Display (Optional but kept)
            Text(
              'Goal: ${widget.targetAmountMl} ml / day',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant, // Muted color
                fontFamily: 'Work Sans',
              ),
            ),
            const SizedBox(height: 20),

            // --- Preset Buttons (Improved) ---
            Text(
              "Quick Add:",
              style: theme.textTheme.labelMedium
                  ?.copyWith(color: theme.colorScheme.onSurfaceVariant),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 10.0, // Horizontal space
              runSpacing: 10.0, // Vertical space
              alignment: WrapAlignment.center,
              children: _presets.entries.map((entry) {
                final key = entry.key;
                final value = entry.value; // Contains amount and icon
                final isSelected = _selectedPresetKey == key;

                // Use FilledButton for selected, FilledButton.tonal or OutlinedButton for others
                return FilledButton.tonalIcon(
                  icon: Icon(value.icon, size: 18),
                  label: Text(
                      '${value.amount}ml'), // Show only amount for brevity? Or keep key? Let's try amount.
                  // label: Text(key), // Alternative: Show name like 'Glass'
                  onPressed: () => _selectPreset(key),
                  style: FilledButton.styleFrom(
                    backgroundColor: isSelected
                        ? accentColor
                        : theme.colorScheme.secondaryContainer.withOpacity(0.5),
                    foregroundColor: isSelected
                        ? Colors.white // Use white for selected button text
                        : theme.colorScheme.onSecondaryContainer,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 14, vertical: 10),
                    textStyle: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Work Sans',
                        fontSize: 13),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0)),
                    // Minimum size ensures buttons are not too small
                    minimumSize: const Size(80, 36),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 24),

            // --- Custom Input Field (Improved) ---
            Text(
              "Custom Amount:",
              style: theme.textTheme.labelMedium
                  ?.copyWith(color: theme.colorScheme.onSurfaceVariant),
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Decrement Button
                _buildIncrementDecrementButton(
                    icon: Icons.remove,
                    onPressed: () => _adjustAmount(-50), // Decrement by 50ml
                    theme: theme,
                    accentColor: accentColor),
                const SizedBox(width: 8),
                // Text Field
                Expanded(
                  child: TextField(
                    controller: _mlController,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: false),
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.digitsOnly
                    ],
                    textAlign: TextAlign.center,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: accentColor,
                      fontFamily: 'Work Sans',
                    ),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(vertical: 16.0), // Make field taller
                      // Use OutlineInputBorder for modern look
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide:
                            BorderSide(color: theme.colorScheme.outline),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide: BorderSide(
                            color: theme.colorScheme.outline.withOpacity(0.5)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                        borderSide: BorderSide(color: accentColor, width: 2),
                      ),
                      suffixText: 'ml',
                      suffixStyle: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontFamily: 'Work Sans',
                      ),
                      // Remove label text, rely on context and placeholder if needed
                      // hintText: '0',
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Increment Button
                _buildIncrementDecrementButton(
                    icon: Icons.add,
                    onPressed: () => _adjustAmount(50), // Increment by 50ml
                    theme: theme,
                    accentColor: accentColor),
              ],
            )
          ],
        ),
      ),

      actions: <Widget>[
        // Cancel Button (TextButton for less emphasis)
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(); // Close dialog, return null
          },
          style: TextButton.styleFrom(
              foregroundColor: theme.colorScheme.onSurfaceVariant),
          child: const Text('Cancel',
              style: TextStyle(
                  fontFamily: 'Work Sans', fontWeight: FontWeight.w500)),
        ),
        // Log Button (FilledButton for primary action)
        FilledButton(
          style: FilledButton.styleFrom(
            backgroundColor: accentColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0), // Consistent rounding
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            textStyle: const TextStyle(fontWeight: FontWeight.bold, fontFamily: 'Work Sans', fontSize: 15),
          ),
          // Disable button if amount is 0 or negative
          onPressed: _amountToLog > 0
              ? () {
                  // Removed log
                  Navigator.of(context)
                      .pop(_amountToLog); // Close dialog, return amount
                }
              : null, // Disable if amount is 0
          child: const Text('Log Water',
              style: TextStyle(
                  fontFamily: 'Work Sans', fontWeight: FontWeight.w600)),
        ),
      ],
    );
  }

  // Helper for +/- buttons
  Widget _buildIncrementDecrementButton(
      {required IconData icon,
      required VoidCallback onPressed,
      required ThemeData theme,
      required Color accentColor}) {
    return SizedBox(
      width: 48, // Fixed width
      height: 48, // Fixed height to match text field
      child: IconButton.filledTonal(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        style: IconButton.styleFrom(
          foregroundColor: accentColor, // Use accent color
          backgroundColor: accentColor.withOpacity(0.1), // Use accent color with opacity
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
        ),
      ),
    );
  }
}

