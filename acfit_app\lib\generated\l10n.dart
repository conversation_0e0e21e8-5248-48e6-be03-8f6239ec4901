import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;


/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/l10n.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
/// dev_dependencies:
///   intl_translation: ^0.17.7
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you'll need to edit this
/// file.
///
/// First, open your project's ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project's Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('de'),
    Locale('zh'),
    Locale('ar'),
  ];

  // App title
  String get appTitle;

  // Tab names
  String get homeTab;
  String get workoutTab;
  String get mealTab;
  String get profileTab;

  // Settings screen
  String get settingsTitle;
  String get generalSection;
  String get notificationsSettings;
  String get personalInfoSettings;
  String get languageSettings;
  String get darkModeSettings;

  // Help & Support section
  String get helpSupportSection;
  String get privacyPolicySettings;
  String get aboutUsSettings;
  String get helpCenterSettings;
  String get submitFeedbackSettings;

  // Danger Zone section
  String get dangerZoneSection;
  String get closeAccountSettings;

  // Log Out section
  String get logOutSection;
  String get signOutSettings;

  // Sign out dialog
  String get confirmSignOutTitle;
  String get confirmSignOutMessage;
  String get cancelButton;
  String get signOutButton;

  // Error messages
  String logoutFailedMessage(String error);

  // Language selection
  String get languageSelectionTitle;
  String get saveButton;
  String languageChangedMessage(String language);

  // Misc
  String get careful;
  String notImplementedYet(String feature);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(_lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'de', 'en', 'es', 'fr', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations _lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'de': return AppLocalizationsDe();
    case 'en': return AppLocalizationsEn();
    case 'es': return AppLocalizationsEs();
    case 'fr': return AppLocalizationsFr();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}

class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn() : super('en');

  @override
  String get appTitle => 'AC-FIT';

  @override
  String get homeTab => 'Home';

  @override
  String get workoutTab => 'Workout';

  @override
  String get mealTab => 'Meal';

  @override
  String get profileTab => 'Profile';

  @override
  String get settingsTitle => 'Account Settings';

  @override
  String get generalSection => 'General';

  @override
  String get notificationsSettings => 'Notifications';

  @override
  String get personalInfoSettings => 'Personal Information';

  @override
  String get languageSettings => 'Language';

  @override
  String get darkModeSettings => 'Dark Mode';

  @override
  String get helpSupportSection => 'Help & Support';

  @override
  String get privacyPolicySettings => 'Privacy Policy';

  @override
  String get aboutUsSettings => 'About Us';

  @override
  String get helpCenterSettings => 'Help Center';

  @override
  String get submitFeedbackSettings => 'Submit Feedback';

  @override
  String get dangerZoneSection => 'Danger Zone';

  @override
  String get closeAccountSettings => 'Close Account';

  @override
  String get logOutSection => 'Log Out';

  @override
  String get signOutSettings => 'Sign Out';

  @override
  String get confirmSignOutTitle => 'Confirm Sign Out';

  @override
  String get confirmSignOutMessage => 'Are you sure you want to sign out?';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get signOutButton => 'Sign Out';

  @override
  String logoutFailedMessage(String error) => 'Logout failed: $error';

  @override
  String get languageSelectionTitle => 'Select Language';

  @override
  String get saveButton => 'Save';

  @override
  String languageChangedMessage(String language) => 'Language changed to $language';

  @override
  String get careful => 'Careful';

  @override
  String notImplementedYet(String feature) => '$feature not implemented yet.';
}

class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs() : super('es');

  @override
  String get appTitle => 'AC-FIT';

  @override
  String get homeTab => 'Inicio';

  @override
  String get workoutTab => 'Entrenamiento';

  @override
  String get mealTab => 'Comida';

  @override
  String get profileTab => 'Perfil';

  @override
  String get settingsTitle => 'Configuración de la cuenta';

  @override
  String get generalSection => 'General';

  @override
  String get notificationsSettings => 'Notificaciones';

  @override
  String get personalInfoSettings => 'Información personal';

  @override
  String get languageSettings => 'Idioma';

  @override
  String get darkModeSettings => 'Modo oscuro';

  @override
  String get helpSupportSection => 'Ayuda y soporte';

  @override
  String get privacyPolicySettings => 'Política de privacidad';

  @override
  String get aboutUsSettings => 'Sobre nosotros';

  @override
  String get helpCenterSettings => 'Centro de ayuda';

  @override
  String get submitFeedbackSettings => 'Enviar comentarios';

  @override
  String get dangerZoneSection => 'Zona de peligro';

  @override
  String get closeAccountSettings => 'Cerrar cuenta';

  @override
  String get logOutSection => 'Cerrar sesión';

  @override
  String get signOutSettings => 'Cerrar sesión';

  @override
  String get confirmSignOutTitle => 'Confirmar cierre de sesión';

  @override
  String get confirmSignOutMessage => '¿Estás seguro de que quieres cerrar sesión?';

  @override
  String get cancelButton => 'Cancelar';

  @override
  String get signOutButton => 'Cerrar sesión';

  @override
  String logoutFailedMessage(String error) => 'Error al cerrar sesión: $error';

  @override
  String get languageSelectionTitle => 'Seleccionar idioma';

  @override
  String get saveButton => 'Guardar';

  @override
  String languageChangedMessage(String language) => 'Idioma cambiado a $language';

  @override
  String get careful => 'Cuidado';

  @override
  String notImplementedYet(String feature) => '$feature aún no implementado.';
}

class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr() : super('fr');

  @override
  String get appTitle => 'AC-FIT';

  @override
  String get homeTab => 'Accueil';

  @override
  String get workoutTab => 'Entraînement';

  @override
  String get mealTab => 'Repas';

  @override
  String get profileTab => 'Profil';

  @override
  String get settingsTitle => 'Paramètres du compte';

  @override
  String get generalSection => 'Général';

  @override
  String get notificationsSettings => 'Notifications';

  @override
  String get personalInfoSettings => 'Informations personnelles';

  @override
  String get languageSettings => 'Langue';

  @override
  String get darkModeSettings => 'Mode sombre';

  @override
  String get helpSupportSection => 'Aide et support';

  @override
  String get privacyPolicySettings => 'Politique de confidentialité';

  @override
  String get aboutUsSettings => 'À propos de nous';

  @override
  String get helpCenterSettings => 'Centre d\'aide';

  @override
  String get submitFeedbackSettings => 'Soumettre des commentaires';

  @override
  String get dangerZoneSection => 'Zone de danger';

  @override
  String get closeAccountSettings => 'Fermer le compte';

  @override
  String get logOutSection => 'Déconnexion';

  @override
  String get signOutSettings => 'Se déconnecter';

  @override
  String get confirmSignOutTitle => 'Confirmer la déconnexion';

  @override
  String get confirmSignOutMessage => 'Êtes-vous sûr de vouloir vous déconnecter?';

  @override
  String get cancelButton => 'Annuler';

  @override
  String get signOutButton => 'Se déconnecter';

  @override
  String logoutFailedMessage(String error) => 'Échec de la déconnexion: $error';

  @override
  String get languageSelectionTitle => 'Sélectionner la langue';

  @override
  String get saveButton => 'Enregistrer';

  @override
  String languageChangedMessage(String language) => 'Langue changée en $language';

  @override
  String get careful => 'Attention';

  @override
  String notImplementedYet(String feature) => '$feature pas encore implémenté.';
}

// Placeholder implementations for other languages
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe() : super('de');

  @override
  String get appTitle => 'AC-FIT';

  @override
  String get homeTab => 'Startseite';

  @override
  String get workoutTab => 'Training';

  @override
  String get mealTab => 'Mahlzeit';

  @override
  String get profileTab => 'Profil';

  @override
  String get settingsTitle => 'Kontoeinstellungen';

  @override
  String get generalSection => 'Allgemein';

  @override
  String get notificationsSettings => 'Benachrichtigungen';

  @override
  String get personalInfoSettings => 'Persönliche Informationen';

  @override
  String get languageSettings => 'Sprache';

  @override
  String get darkModeSettings => 'Dunkler Modus';

  @override
  String get helpSupportSection => 'Hilfe & Support';

  @override
  String get privacyPolicySettings => 'Datenschutzrichtlinie';

  @override
  String get aboutUsSettings => 'Über uns';

  @override
  String get helpCenterSettings => 'Hilfezentrum';

  @override
  String get submitFeedbackSettings => 'Feedback senden';

  @override
  String get dangerZoneSection => 'Gefahrenzone';

  @override
  String get closeAccountSettings => 'Konto schließen';

  @override
  String get logOutSection => 'Abmelden';

  @override
  String get signOutSettings => 'Abmelden';

  @override
  String get confirmSignOutTitle => 'Abmeldung bestätigen';

  @override
  String get confirmSignOutMessage => 'Sind Sie sicher, dass Sie sich abmelden möchten?';

  @override
  String get cancelButton => 'Abbrechen';

  @override
  String get signOutButton => 'Abmelden';

  @override
  String logoutFailedMessage(String error) => 'Abmeldung fehlgeschlagen: $error';

  @override
  String get languageSelectionTitle => 'Sprache auswählen';

  @override
  String get saveButton => 'Speichern';

  @override
  String languageChangedMessage(String language) => 'Sprache geändert zu $language';

  @override
  String get careful => 'Vorsicht';

  @override
  String notImplementedYet(String feature) => '$feature noch nicht implementiert.';
}

class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh() : super('zh');

  @override
  String get appTitle => 'AC-FIT';

  @override
  String get homeTab => '首页';

  @override
  String get workoutTab => '锻炼';

  @override
  String get mealTab => '餐饮';

  @override
  String get profileTab => '个人资料';

  @override
  String get settingsTitle => '账户设置';

  @override
  String get generalSection => '通用';

  @override
  String get notificationsSettings => '通知';

  @override
  String get personalInfoSettings => '个人信息';

  @override
  String get languageSettings => '语言';

  @override
  String get darkModeSettings => '深色模式';

  @override
  String get helpSupportSection => '帮助与支持';

  @override
  String get privacyPolicySettings => '隐私政策';

  @override
  String get aboutUsSettings => '关于我们';

  @override
  String get helpCenterSettings => '帮助中心';

  @override
  String get submitFeedbackSettings => '提交反馈';

  @override
  String get dangerZoneSection => '危险区域';

  @override
  String get closeAccountSettings => '关闭账户';

  @override
  String get logOutSection => '退出登录';

  @override
  String get signOutSettings => '退出登录';

  @override
  String get confirmSignOutTitle => '确认退出登录';

  @override
  String get confirmSignOutMessage => '您确定要退出登录吗？';

  @override
  String get cancelButton => '取消';

  @override
  String get signOutButton => '退出登录';

  @override
  String logoutFailedMessage(String error) => '退出登录失败: $error';

  @override
  String get languageSelectionTitle => '选择语言';

  @override
  String get saveButton => '保存';

  @override
  String languageChangedMessage(String language) => '语言已更改为 $language';

  @override
  String get careful => '注意';

  @override
  String notImplementedYet(String feature) => '$feature 尚未实现。';
}

class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr() : super('ar');

  @override
  String get appTitle => 'AC-FIT';

  @override
  String get homeTab => 'الرئيسية';

  @override
  String get workoutTab => 'التمرين';

  @override
  String get mealTab => 'الوجبة';

  @override
  String get profileTab => 'الملف الشخصي';

  @override
  String get settingsTitle => 'إعدادات الحساب';

  @override
  String get generalSection => 'عام';

  @override
  String get notificationsSettings => 'الإشعارات';

  @override
  String get personalInfoSettings => 'المعلومات الشخصية';

  @override
  String get languageSettings => 'اللغة';

  @override
  String get darkModeSettings => 'الوضع المظلم';

  @override
  String get helpSupportSection => 'المساعدة والدعم';

  @override
  String get privacyPolicySettings => 'سياسة الخصوصية';

  @override
  String get aboutUsSettings => 'من نحن';

  @override
  String get helpCenterSettings => 'مركز المساعدة';

  @override
  String get submitFeedbackSettings => 'إرسال ملاحظات';

  @override
  String get dangerZoneSection => 'منطقة الخطر';

  @override
  String get closeAccountSettings => 'إغلاق الحساب';

  @override
  String get logOutSection => 'تسجيل الخروج';

  @override
  String get signOutSettings => 'تسجيل الخروج';

  @override
  String get confirmSignOutTitle => 'تأكيد تسجيل الخروج';

  @override
  String get confirmSignOutMessage => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get cancelButton => 'إلغاء';

  @override
  String get signOutButton => 'تسجيل الخروج';

  @override
  String logoutFailedMessage(String error) => 'فشل تسجيل الخروج: $error';

  @override
  String get languageSelectionTitle => 'اختر اللغة';

  @override
  String get saveButton => 'حفظ';

  @override
  String languageChangedMessage(String language) => 'تم تغيير اللغة إلى $language';

  @override
  String get careful => 'احذر';

  @override
  String notImplementedYet(String feature) => '$feature غير متوفر بعد.';
}
