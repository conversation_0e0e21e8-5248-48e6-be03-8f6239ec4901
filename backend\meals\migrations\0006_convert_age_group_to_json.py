# Generated by Django 5.2.1 on 2025-05-31 17:30

from django.db import migrations
import json


def convert_age_group_to_json(apps, schema_editor):
    """Convert string age_group values to JSON format"""
    MealPlan = apps.get_model('meals', 'MealPlan')

    for plan in MealPlan.objects.all():
        if plan.age_group and isinstance(plan.age_group, str):
            # Convert string to JSON format
            plan.age_group = json.dumps(plan.age_group)
            plan.save(update_fields=['age_group'])


def reverse_age_group_conversion(apps, schema_editor):
    """Reverse the conversion - convert JSON back to string"""
    MealPlan = apps.get_model('meals', 'MealPlan')

    for plan in MealPlan.objects.all():
        if plan.age_group:
            try:
                # Try to parse as JSON and convert back to string
                parsed = json.loads(plan.age_group)
                plan.age_group = str(parsed)
                plan.save(update_fields=['age_group'])
            except (json.JSO<PERSON><PERSON><PERSON>Error, TypeError):
                # If it's already a string, leave it as is
                pass


class Migration(migrations.Migration):

    dependencies = [
        ('meals', '0004_remove_meal_image_url'),
    ]

    operations = [
        migrations.RunPython(convert_age_group_to_json, reverse_age_group_conversion),
    ]
