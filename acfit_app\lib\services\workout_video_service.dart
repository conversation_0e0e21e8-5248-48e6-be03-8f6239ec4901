import 'package:dio/dio.dart';
import '../models/workout_video.dart';
import '../utils/logger.dart';
import '../services/auth_service.dart';

class WorkoutVideoService {
  static final Dio _dio = Dio();
  static const String _baseUrl = 'http://127.0.0.1:8000/api/workouts/videos';

  /// Get videos for the user's active workout plan
  static Future<List<WorkoutVideo>> getVideosForActiveWorkoutPlan() async {
    try {
      final authService = AuthService();
      final token = await authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await _dio.get(
        '$_baseUrl/',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        // Handle both paginated and direct list responses
        final responseData = response.data;
        List<dynamic> videosJson;

        if (responseData is Map && responseData.containsKey('results')) {
          // Paginated response from DRF
          videosJson = responseData['results'] as List<dynamic>;
        } else if (responseData is List) {
          // Direct list response
          videosJson = responseData;
        } else {
          Logger.log('Unexpected response format for workout videos');
          return [];
        }

        return videosJson.map((json) => WorkoutVideo.fromJson(json)).toList();
      } else {
        Logger.log('Failed to fetch workout videos: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.log('Error fetching workout videos: $e');
      if (e.toString().contains('401') ||
          e.toString().contains('Unauthorized')) {
        Logger.log('Authentication error - user may need to log in again');
      }
      return [];
    }
  }

  /// Get videos for a specific workout plan
  static Future<List<WorkoutVideo>> getVideosForWorkoutPlan(int planId) async {
    try {
      final authService = AuthService();
      final token = await authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await _dio.get(
        '$_baseUrl/by-plan/$planId/',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> videosJson = response.data;
        return videosJson.map((json) => WorkoutVideo.fromJson(json)).toList();
      } else {
        Logger.log(
            'Failed to fetch workout videos for plan $planId: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      Logger.log('Error fetching workout videos for plan $planId: $e');
      return [];
    }
  }

  /// Get a specific video by ID
  static Future<WorkoutVideo?> getVideoById(int videoId) async {
    try {
      final authService = AuthService();
      final token = await authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await _dio.get(
        '$_baseUrl/$videoId/',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return WorkoutVideo.fromJson(response.data);
      } else {
        Logger.log('Failed to fetch video $videoId: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      Logger.log('Error fetching video $videoId: $e');
      return null;
    }
  }
}
