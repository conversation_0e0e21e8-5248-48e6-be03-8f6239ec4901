import 'package:flutter/material.dart';
import 'dart:async'; // Import for StreamSubscription
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart'; // Import table_calendar
import 'package:cached_network_image/cached_network_image.dart';
// import 'dart:developer' as developer; // Import developer for logging (removed)

import '../../providers/workout_provider.dart';
// Removed auth_provider import
import '../../models/workout_log.dart';
import '../../models/workout.dart'; // Import WorkoutDay, WorkoutSection, WorkoutExerciseLog
import '../../services/navigation_service.dart'; // Import NavigationService
import '../../services/notification_service.dart'; // Import for notifications
import '../../utils/logger.dart'; // Import Logger for logging
// Removed date_utils import

class WorkoutScreen extends StatefulWidget {
  const WorkoutScreen({Key? key}) : super(key: key);

  // Add a method to refresh data from outside
  void refreshData() {
    // Find the current state and call _fetchWorkoutDataForSelectedDate
    final state = _WorkoutScreenState.instance;
    if (state != null) {
      state._fetchWorkoutDataForSelectedDate();
    }
  }

  @override
  _WorkoutScreenState createState() => _WorkoutScreenState();
}

class _WorkoutScreenState extends State<WorkoutScreen> {
  // Static instance for access from widget
  static _WorkoutScreenState? instance;

  // Calendar state
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay; // Use nullable DateTime for selected day
  CalendarFormat _calendarFormat = CalendarFormat.week;

  // Subscriptions for various notifications
  StreamSubscription? _workoutCompletedSubscription;
  StreamSubscription? _workoutLogUpdatedSubscription;
  StreamSubscription? _clearWorkoutCacheSubscription;
  StreamSubscription? _workoutDataRefreshedSubscription;

  @override
  void initState() {
    super.initState();
    // Set the static instance
    instance = this;

    _selectedDay = _focusedDay; // Select today initially

    // Listen for workout completion notifications
    _workoutCompletedSubscription =
        NotificationService.instance.on('workout_completed').listen((data) {
      // Refresh data when a workout is completed

      _fetchWorkoutDataForSelectedDate();
    });

    // Also listen for workout log updates
    _workoutLogUpdatedSubscription =
        NotificationService.instance.on('workout_log_updated').listen((data) {
      // Refresh data when a workout log is updated

      _fetchWorkoutDataForSelectedDate();
    });

    // Listen for cache clear notifications
    _clearWorkoutCacheSubscription =
        NotificationService.instance.on('clear_workout_cache').listen((_) {
      // Refresh data when cache is cleared

      _fetchWorkoutDataForSelectedDate();
    });

    // Listen for workout data refreshed notifications
    _workoutDataRefreshedSubscription =
        NotificationService.instance.on('workout_data_refreshed').listen((_) {
      // Refresh data when workout data is refreshed

      _fetchWorkoutDataForSelectedDate();
    });

    // Fetch initial data for today with a slight delay to ensure the provider is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Add a small delay to ensure the provider is initialized
      Future.delayed(const Duration(milliseconds: 100), () {
        _fetchWorkoutDataForSelectedDate();
      });
    });
  }

  @override
  void dispose() {
    // Clear the static instance if it's this instance
    if (instance == this) {
      instance = null;
    }

    // Cancel all subscriptions when screen is disposed
    _workoutCompletedSubscription?.cancel();
    _workoutLogUpdatedSubscription?.cancel();
    _clearWorkoutCacheSubscription?.cancel();
    _workoutDataRefreshedSubscription?.cancel();

    super.dispose();
  }

  Future<void> _fetchWorkoutDataForSelectedDate() async {
    // Added check for mounted state before accessing context
    if (!mounted) return;
    if (_selectedDay == null) return; // Don't fetch if no day is selected

    try {
      final provider = Provider.of<WorkoutProvider>(context, listen: false);
      // Ensure the date is passed correctly to the provider method
      final selectedDateOnly = DateUtils.dateOnly(_selectedDay!);

      await provider.fetchWorkoutForDate(selectedDateOnly, forceRefresh: true);

      // If we're looking at today's date, also prefetch the next 7 days
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      if (DateUtils.isSameDay(_selectedDay!, today)) {
        for (int i = 1; i <= 7; i++) {
          final futureDate = today.add(Duration(days: i));
          await provider.fetchWorkoutForDate(futureDate, forceRefresh: false);
        }
      }

      // Force a rebuild of the UI
      if (mounted) {
        setState(() {
          // Just trigger a rebuild
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading workout data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Builds the TableCalendar widget (adapted from MealPlanScreen)
  Widget _buildCalendar() {
    return TableCalendar(
      firstDay: DateTime.utc(2020, 1, 1), // Example start date
      lastDay: DateTime.utc(2030, 12, 31), // Example end date
      focusedDay: _focusedDay,
      selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
      calendarFormat: _calendarFormat,
      startingDayOfWeek: StartingDayOfWeek.monday,
      rowHeight: 40, // Reduced row height
      daysOfWeekHeight: 20, // Reduced days of week height
      headerStyle: const HeaderStyle(
        formatButtonVisible: false, // Hide format button
        titleCentered: true,
        titleTextStyle: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontFamily: 'Work Sans',
            fontWeight: FontWeight.w600),
        leftChevronIcon:
            Icon(Icons.chevron_left, color: Colors.white, size: 18),
        rightChevronIcon:
            Icon(Icons.chevron_right, color: Colors.white, size: 18),
        headerPadding: EdgeInsets.symmetric(vertical: 4.0),
      ),
      calendarStyle: const CalendarStyle(
        // Customize appearance
        defaultTextStyle: TextStyle(
            color: Colors.white70, fontFamily: 'Work Sans', fontSize: 12),
        weekendTextStyle: TextStyle(
            color: Colors.white54, fontFamily: 'Work Sans', fontSize: 12),
        selectedTextStyle: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontFamily: 'Work Sans'),
        todayTextStyle: TextStyle(
            color: Color(0xFFF97316), // Use the orange color for today
            fontWeight: FontWeight.bold,
            fontFamily: 'Work Sans'),
        selectedDecoration: BoxDecoration(
          color: Colors.white, // White circle for selected
          shape: BoxShape.circle,
        ),
        todayDecoration: BoxDecoration(
          // Orange circle for today
          color: Color.fromRGBO(
              249, 115, 22, 0.5), // Using RGBA instead of withOpacity
          shape: BoxShape.circle,
        ),
        outsideDaysVisible: false,
      ),
      daysOfWeekStyle: const DaysOfWeekStyle(
        weekdayStyle: TextStyle(
            color: Colors.white54, fontFamily: 'Work Sans', fontSize: 10),
        weekendStyle: TextStyle(
            color: Colors.white38, fontFamily: 'Work Sans', fontSize: 10),
        decoration: BoxDecoration(color: Colors.transparent),
      ),
      // Allow selecting any date within a reasonable range
      enabledDayPredicate: (DateTime date) {
        // Allow selecting dates from 30 days in the past to 7 days in the future
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final thirtyDaysAgo = today.subtract(const Duration(days: 30));
        final sevenDaysLater = today.add(const Duration(days: 7));

        // Enable dates between 30 days ago and 7 days in the future
        return !date.isBefore(thirtyDaysAgo) && !date.isAfter(sevenDaysLater);
      },
      onDaySelected: (selectedDay, focusedDay) {
        if (!isSameDay(_selectedDay, selectedDay)) {
          setState(() {
            _selectedDay = selectedDay;
            _focusedDay = focusedDay; // update `_focusedDay` here as well
          });
          _fetchWorkoutDataForSelectedDate(); // Load data for the newly selected date
        }
      },
      onFormatChanged: (format) {
        if (_calendarFormat != format) {
          setState(() {
            _calendarFormat = format;
          });
        }
      },
      onPageChanged: (focusedDay) {
        // No need to call `setState()` here
        _focusedDay = focusedDay;
      },
      calendarBuilders: CalendarBuilders(
        // Style disabled days differently (e.g., greyed out)
        disabledBuilder: (context, day, focusedDay) {
          // Calculate program day
          final now = DateTime.now();
          final today = DateTime(now.year, now.month, now.day);
          final daysSinceToday = day.difference(today).inDays;
          final programDay =
              daysSinceToday + 1; // Day 1 is today, Day 2 is tomorrow, etc.

          // For future days beyond day 7, show as locked
          final isFutureDay = day.isAfter(today);
          final isWithinFirstWeek = programDay <= 7 && programDay > 0;

          // Use a different style for disabled days but keep the number visible
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${day.day}',
                  style: const TextStyle(
                      color: Colors.grey,
                      fontFamily: 'Work Sans',
                      fontSize: 12),
                ),
                if (isFutureDay)
                  Text(
                    isWithinFirstWeek ? 'Day $programDay' : 'Locked',
                    style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 8,
                        fontFamily: 'Work Sans'),
                  ),
              ],
            ),
          );
        },
        // Ensure today still gets its special style even if disabled for selection initially
        todayBuilder: (context, day, focusedDay) {
          // Check if it's also the selected day
          final isSelected = isSameDay(_selectedDay, day);
          return Center(
            child: Container(
              decoration: BoxDecoration(
                // Use today's highlight unless it's selected, then use selected style
                color: isSelected
                    ? Colors.white
                    : const Color.fromRGBO(
                        249, 115, 22, 0.5), // Using RGBA instead of withOpacity
                shape: BoxShape.circle,
              ),
              width: 26,
              height: 26,
              child: Center(
                child: Text(
                  '${day.day}',
                  style: TextStyle(
                      // Use selected text style if selected, otherwise today's style
                      color:
                          isSelected ? Colors.black : const Color(0xFFF97316),
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Work Sans',
                      fontSize: 12),
                ),
              ),
            ),
          );
        },
        // Ensure selected day retains its style even if it's today
        selectedBuilder: (context, day, focusedDay) {
          return Center(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white, // Selected style
                shape: BoxShape.circle,
              ),
              width: 26,
              height: 26,
              child: Center(
                child: Text(
                  '${day.day}',
                  style: const TextStyle(
                      color: Colors.black, // Selected text style
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Work Sans',
                      fontSize: 12),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Builds the header part (Title, Calendar) - Adapted from MealPlanScreen
  Widget _buildHeader() {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/homebg.png'),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(
            Color.fromRGBO(16, 17, 20, 0.8), // Semi-transparent dark overlay
            BlendMode.srcOver,
          ),
        ),
      ),
      width: double.infinity,
      // Don't set a fixed height, let it size based on content
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(
                16.0, 50.0, 16.0, 0.0), // Increased top padding
            child: Row(
              mainAxisAlignment: MainAxisAlignment
                  .spaceBetween, // Use spaceBetween to align title and settings
              children: [
                // Screen Title
                const Text(
                  'My Workouts',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontFamily: 'Work Sans',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                // Settings Button
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(18),
                    color: const Color.fromRGBO(35, 37, 43, 1),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.settings_outlined,
                        color: Colors.white),
                    tooltip: 'Settings',
                    onPressed: () =>
                        NavigationService.navigateToNamed('/settings'),
                    padding: const EdgeInsets.all(8.0),
                    constraints: const BoxConstraints(),
                    iconSize: 20,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8), // Add spacing after the header row
          _buildCalendar(), // Add TableCalendar widget here
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final workoutProvider = Provider.of<WorkoutProvider>(context);

    // Create a date-only version of the selected day to match the keys in workoutData
    DateTime? selectedDateOnly;
    if (_selectedDay != null) {
      selectedDateOnly =
          DateTime(_selectedDay!.year, _selectedDay!.month, _selectedDay!.day);
    }

    // Get the workout data for the selected day using the date-only key
    // Use var instead of final to allow reassignment if we find a matching date
    var workoutDataForSelectedDate = selectedDateOnly != null
        ? workoutProvider.workoutData[selectedDateOnly]
        : null;

    // Log the available dates in the workout data map
    if (_selectedDay != null) {
      // Debug log to help diagnose the issue
      if (workoutDataForSelectedDate == null) {
        // Try to find a matching date by comparing year, month, and day
        final matchingEntry = workoutProvider.workoutData.entries.firstWhere(
          (entry) =>
              entry.key.year == selectedDateOnly!.year &&
              entry.key.month == selectedDateOnly.month &&
              entry.key.day == selectedDateOnly.day,
          orElse: () => MapEntry(
              DateTime(1970),
              TodaysWorkoutResponse(
                  date: '', workoutLogs: [], isRestDay: true)),
        );

        if (matchingEntry.key != DateTime(1970)) {
          // Use the matching entry's value instead
          workoutDataForSelectedDate = matchingEntry.value;
        } else {
          // If we still don't have data, trigger a fetch for this date
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _fetchWorkoutDataForSelectedDate();
          });
        }
      } else {}
    }

    final isLoading = workoutProvider.isLoading;

    return Scaffold(
      backgroundColor: const Color.fromRGBO(16, 17, 20, 1), // Dark background
      body: SafeArea(
        top: false, // Remove top padding to maximize space
        // Wrap body with SafeArea
        child: Column(
          children: [
            _buildHeader(), // Use the new header method
            // Content Area
            Expanded(
              child: Container(
                width: double.infinity, // Take full width
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(40),
                    topRight: Radius.circular(40),
                  ),
                  color: Colors.white, // White background for content
                ),
                margin: const EdgeInsets.only(
                    top:
                        16), // Add margin to create space between calendar and content
                child: isLoading
                    ? const Center(
                        child: CircularProgressIndicator(
                            color: Color.fromRGBO(255, 127, 54, 1)))
                    : _buildWorkoutContent(workoutDataForSelectedDate),
              ), // Closes Container
            ), // Closes Expanded
          ],
        ), // Closes Column
      ), // Closes SafeArea
    ); // Closes Scaffold
  }

  // Build a card for a workout session
  Widget _buildSessionCard(
    WorkoutSession session,
    List<WorkoutLog> allLogs, // Accept the list of all logs for the day
    String? coverImageUrl,
  ) {
    // Find the specific log for this session to check completion status
    WorkoutLog? specificLogForCompletionCheck;
    try {
      specificLogForCompletionCheck =
          allLogs.firstWhere((log) => log.workoutSession == session.id);
    } catch (e) {
      // If we can't find a specific log for this session, check if there's a log for the workout day
      try {
        specificLogForCompletionCheck =
            allLogs.firstWhere((log) => log.workoutDay?.id == session.id);
      } catch (e) {
        // If we still can't find a log, check if there's any completed log for this day
        final completedLogs = allLogs.where((log) => log.isCompleted).toList();
        if (completedLogs.isNotEmpty) {
          specificLogForCompletionCheck = completedLogs.first;
        } else {
          specificLogForCompletionCheck = null;
        }
      }
    }

    // Check if this specific session is completed using the helper method
    bool isSessionCompleted = false;

    // First check if any log has this session completed
    for (final log in allLogs) {
      if (log.isSessionCompleted(session.id)) {
        isSessionCompleted = true;

        break;
      }
    }

    // Check if this is a future workout using the specific log's date if available
    bool isFutureWorkout = false;
    if (specificLogForCompletionCheck != null) {
      try {
        final dateStr = specificLogForCompletionCheck
            .date; // Use specificLogForCompletionCheck.date
        final logDate = DateTime.parse(dateStr);
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final daysSinceToday = logDate.difference(today).inDays;
        isFutureWorkout = daysSinceToday > 0;
      } catch (e) {
        // If there's an error parsing the date, default to not future
        isFutureWorkout = false;
      }
    }

    return GestureDetector(
      // Only allow clicking if it's completed or today's workout
      onTap: () {
        // Check if this is a future workout
        if (isFutureWorkout && !isSessionCompleted) {
          // Don't do anything for future workouts
          return;
        }

        // Find the specific WorkoutLog for this session from the provided list
        WorkoutLog? specificLog;
        try {
          // Try to find a matching log
          specificLog =
              allLogs.firstWhere((log) => log.workoutSession == session.id);
        } catch (e) {
          // No specific log found, create a temporary one with the session ID

          if (allLogs.isNotEmpty) {
            // Use the first log as a template and create a copy with the session ID
            // Also ensure the workoutDay is set correctly using the session data
            // First, get the actual session data from the API response
            WorkoutSession? apiSession;

            // Find the matching session in the API response
            final workoutProvider =
                Provider.of<WorkoutProvider>(context, listen: false);
            final selectedDate = _selectedDay ?? DateTime.now();
            final dateKey = DateUtils.dateOnly(selectedDate);
            final workoutDataForDate = workoutProvider.workoutData[dateKey];

            if (workoutDataForDate?.sessions != null) {
              apiSession = workoutDataForDate!.sessions!
                  .firstWhere((s) => s.id == session.id, orElse: () => session);
            } else {
              apiSession = session;
            }

            // Create a WorkoutDay with the session data
            WorkoutDay workoutDay = WorkoutDay(
              id: session.id, // Use session ID as a fallback
              name: session.name,
              description: session.description,
              isRestDay: false,
              sessions: [
                apiSession
              ], // Include the current session with all its sections and exercises
            );

            specificLog = allLogs.first.copyWith(
              workoutSession: session.id,
              workoutDay: workoutDay,
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text('Error loading session details.'),
                  backgroundColor: Colors.red),
            );
            return; // Don't navigate if we can't create a log
          }
        }

        // Log detailed information about the session and logs

        // Log details of each log for debugging
        for (int i = 0; i < allLogs.length; i++) {
          final log = allLogs[i];
        }

        // Navigate to workout details screen, passing the specific log in a list
        // Make sure specificLog is not null before using it
        NavigationService.navigateToNamed(
          NavigationService.workoutDetails,
          arguments: {
            'workout': {
              'name': session.name,
              'description': session.description
            },
            // Pass the specific log and all other logs for the day
            'workoutLogs': [
              specificLog,
              ...allLogs.where((log) => log.id != specificLog!.id).toList()
            ],
            // Pass the target session ID to show only this session
            'targetSessionId': session.id,
          },
        );
      
        // Removed unreachable code
      },
      child: Stack(
        children: [
          // Main container
          Container(
            margin: const EdgeInsets.only(bottom: 12.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Session image
                if (coverImageUrl != null && coverImageUrl.isNotEmpty)
                  Container(
                    height: 160,
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      child: CachedNetworkImage(
                        imageUrl: coverImageUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(
                              Icons.fitness_center,
                              size: 40,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                // Session details
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              session.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF393B42),
                              ),
                            ),
                          ),
                          if (session.scheduledTime != null)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 4.0),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF3F3F3),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.access_time,
                                    size: 16,
                                    color: Color(0xFF676B74),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    session.getFormattedScheduledTime() ??
                                        '--:--',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF676B74),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                      if (session.description != null &&
                          session.description!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            session.description!,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF676B74),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          if (session.duration != null)
                            _buildInfoChip(Icons.timer_outlined,
                                '${session.duration} min'),
                          const SizedBox(width: 8),
                          if (session.caloriesBurnEstimate != null)
                            _buildInfoChip(Icons.local_fire_department,
                                '${session.caloriesBurnEstimate} cal'),
                          const SizedBox(width: 8),
                          _buildInfoChip(Icons.fitness_center,
                              '${session.sections?.length ?? 0} sections'),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Completion badge
          Positioned(
            top: 12,
            right: 12,
            child: isSessionCompleted
                ? Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: const [
                        BoxShadow(
                          color: Color.fromRGBO(0, 0, 0, 0.15),
                          spreadRadius: 0,
                          blurRadius: 2,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: Colors.green.shade600,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Completed',
                          style: TextStyle(
                            color: Colors.green.shade800,
                            fontWeight: FontWeight.bold,
                            fontSize: 11,
                            fontFamily: 'Work Sans',
                          ),
                        ),
                      ],
                    ),
                  )
                : isFutureWorkout
                    ? Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: const [
                            BoxShadow(
                              color: Color.fromRGBO(0, 0, 0, 0.15),
                              spreadRadius: 0,
                              blurRadius: 2,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.update_disabled,
                              color: Colors.grey.shade600,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Future',
                              style: TextStyle(
                                color: Colors.grey.shade800,
                                fontWeight: FontWeight.bold,
                                fontSize: 11,
                                fontFamily: 'Work Sans',
                              ),
                            ),
                          ],
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color.fromRGBO(255, 255, 255, 0.9),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: const [
                            BoxShadow(
                              color: Color.fromRGBO(0, 0, 0, 0.15),
                              spreadRadius: 0,
                              blurRadius: 2,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.fitness_center,
                              color: Colors.orange.shade600,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Pending',
                              style: TextStyle(
                                color: Colors.orange.shade800,
                                fontWeight: FontWeight.bold,
                                fontSize: 11,
                                fontFamily: 'Work Sans',
                              ),
                            ),
                          ],
                        ),
                      ),
          ),

          // Future workout overlay
          if (isFutureWorkout && !isSessionCompleted)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 0, 0, 0.5),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.update_disabled,
                        color: Colors.white,
                        size: 40,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Future Workout',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Only available on the scheduled day',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          // Debug overlay for workout log data
        ],
      ),
    );
  }

  // Helper method to build info chips
  Widget _buildInfoChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: const Color(0xFFF3F3F3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: const Color(0xFF676B74),
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF676B74),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutContent(TodaysWorkoutResponse? workoutData) {
    // Handle null workout data
    if (workoutData == null) {
      // Trigger a fetch if we don't have data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _fetchWorkoutDataForSelectedDate();
        }
      });

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
                color: Color.fromRGBO(255, 127, 54, 1)),
            const SizedBox(height: 16),
            Text(
              'Loading workout data for ${_selectedDay?.toString().split(' ')[0] ?? 'today'}...',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    // Handle case where no plan is assigned (new flag)
    if (workoutData.noPlanAssigned) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.fitness_center,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Workout Plan Assigned',
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              workoutData.message ??
                  'Complete the questionnaire to get a personalized workout plan.',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    // Handle rest day
    if (workoutData.isRestDay) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.self_improvement,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Rest Day',
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Take time to recover and prepare for your next workout.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    // Log the workout data for debugging

    // Check if there are no workout logs and no sessions
    if (workoutData.workoutLogs.isEmpty &&
        (workoutData.sessions == null || workoutData.sessions!.isEmpty)) {
      return const Center(
        child: Text(
          'No workout scheduled for this day.',
          style: TextStyle(fontSize: 18, color: Colors.grey),
        ),
      );
    }

    // Use the workout log if available, otherwise show a message
    WorkoutLog? workoutLog;

    if (workoutData.workoutLogs.isNotEmpty) {
      // Use the first workout log
      workoutLog = workoutData.workoutLogs.first;
    }

    // Workout data loaded

    List<Widget> workoutItems = [];

    // Add date indicator if not today
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDate =
        _selectedDay != null ? DateUtils.dateOnly(_selectedDay!) : today;

    if (!DateUtils.isSameDay(selectedDate, today)) {
      final dayDifference = selectedDate.difference(today).inDays;
      String dateText;
      if (dayDifference == 1) {
        dateText = 'Tomorrow';
      } else {
        dateText = '$dayDifference days from now';
      }

      workoutItems.add(
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
          decoration: BoxDecoration(
            color: const Color.fromRGBO(255, 127, 54, 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color.fromRGBO(255, 127, 54, 0.3)),
          ),
          child: Text(
            dateText,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color.fromRGBO(255, 127, 54, 1),
            ),
          ),
        ),
      );
    }

    // Add workout day title if workoutLog exists
    if (workoutLog != null) {
      workoutItems.add(
        Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
          child: Text(
            workoutLog.workoutDay?.name ?? 'Workout',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF393B42),
            ),
          ),
        ),
      );

      // Add workout day description if available
      if (workoutLog.workoutDay?.description != null &&
          workoutLog.workoutDay!.description!.isNotEmpty) {
        workoutItems.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              workoutLog.workoutDay!.description!,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF676B74),
              ),
            ),
          ),
        );
      }
    } else {
      // Add a generic title if no workout log exists
      workoutItems.add(
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
          child: Text(
            'Workout Sessions',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF393B42),
            ),
          ),
        ),
      );
    }

    workoutItems.add(const SizedBox(height: 16));

    // Display individual workout sessions
    // Prioritize sessions from workoutData over those in workoutLog
    List<WorkoutSession> sessionsToDisplay = [];

    // First check if workoutData has sessions
    if (workoutData.sessions != null && workoutData.sessions!.isNotEmpty) {
      sessionsToDisplay = workoutData.sessions!;
    }
    // Then check if workoutLog exists and has sessions
    else if (workoutLog != null &&
        workoutLog.workoutDay?.sessions != null &&
        workoutLog.workoutDay!.sessions!.isNotEmpty) {
      sessionsToDisplay = workoutLog.workoutDay!.sessions!;
    }

    // Check if any workout logs are completed
    bool anyWorkoutCompleted =
        workoutData.workoutLogs.any((log) => log.isCompleted);
    if (anyWorkoutCompleted) {}

    // If we have sessions to display, show them
    if (sessionsToDisplay.isNotEmpty) {
      // Add session title
      workoutItems.add(
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            'Today\'s Sessions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF393B42),
            ),
          ),
        ),
      );

      // Add each session as a card
      for (var session in sessionsToDisplay) {
        workoutItems.add(
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: _buildSessionCard(
                session,
                workoutData.workoutLogs, // Pass all available workout logs
                workoutData.workoutPlanCoverImageUrl),
          ),
        );
      }
    } else {
      // If no sessions are available, show a message
      workoutItems.add(
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
          child: Center(
            child: Text(
              'No workout sessions available for this day.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ),
        ),
      );
    }

    // REMOVED "Complete Workout" button and associated logic

    // Build the list view
    return ListView(
      padding: const EdgeInsets.only(
          top: 32.0, bottom: 24.0), // Increased top padding
      children: workoutItems,
    );
  }

  // REMOVED _handleCompleteWorkout method
}
