import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/local_notification_service.dart'; // Import the service
import 'dart:developer' as developer;

class NotificationsSettingsScreen extends StatefulWidget {
  const NotificationsSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsSettingsScreen> createState() =>
      _NotificationsSettingsScreenState();
}

class _NotificationsSettingsScreenState
    extends State<NotificationsSettingsScreen> {
  bool _isLoading = true;
  bool _masterNotifications = true;
  bool _workoutReminders = true;
  bool _mealReminders = true;
  bool _hydrationReminders = true; // Renamed from _miscNotifications

  // Keys for shared preferences
  static const String _masterKey = 'notifications_master_enabled';
  static const String _workoutKey = 'notifications_workout_enabled';
  static const String _mealKey = 'notifications_meal_enabled';
  static const String _hydrationKey = 'notifications_hydration_enabled';

  // Consistent Styling Values
  final Color _itemBackgroundColor = const Color.fromRGBO(243, 243, 243, 1);
  final BorderRadius _itemBorderRadius = BorderRadius.circular(24);
  final EdgeInsets _itemPadding = const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16.0);
  final EdgeInsets _switchTilePadding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0); // Padding inside the Material

  // Consistent Accent Color
  final Color _accentColor = const Color(0xFFF97316);

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _masterNotifications = prefs.getBool(_masterKey) ?? true;
        _workoutReminders = prefs.getBool(_workoutKey) ?? true;
        _mealReminders = prefs.getBool(_mealKey) ?? true;
        _hydrationReminders = prefs.getBool(_hydrationKey) ?? true;
        _isLoading = false;
      });
    } catch (e) {
      developer.log('Error loading notification settings: $e', name: 'NotificationsSettingsScreen');
      setState(() {
        _isLoading = false; // Still finish loading even if error
      });
    }
  }

  Future<void> _updateSetting(String key, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(key, value);
    } catch (e) {
      developer.log('Error saving notification setting ($key): $e', name: 'NotificationsSettingsScreen');
      // Optionally show an error to the user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error saving setting.'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final titleStyle = theme.textTheme.titleMedium; // Consistent title style
    final subtitleStyle = theme.textTheme.bodySmall; // Consistent subtitle style

    return Scaffold(
      // Use white background for consistency with other settings screens
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: const Color.fromRGBO(16, 17, 20, 1), // Keep dark header
        foregroundColor: Colors.white,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0, // Remove shadow for flatter look consistent with others
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Color(0xFFF97316))) // Use accent color
          : ListView(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16.0), // Remove horizontal padding here, apply per item
              children: [
                _buildSectionHeader('Master Control'),
                Padding(
                  padding: _itemPadding,
                  child: Material(
                    color: _itemBackgroundColor,
                    borderRadius: _itemBorderRadius,
                    child: SwitchListTile(
                      contentPadding: _switchTilePadding, // Inner padding
                      title: Text('Enable All Notifications', style: titleStyle),
                      subtitle: Text(
                        _masterNotifications
                            ? 'Receive all app notifications.'
                            : 'All app notifications are currently disabled.',
                        style: subtitleStyle,
                      ),
                      value: _masterNotifications,
                      onChanged: (bool value) {
                        setState(() {
                          _masterNotifications = value;
                          // Also update individual toggles if master is turned off
                          if (!value) {
                            _workoutReminders = false;
                            _mealReminders = false;
                            _hydrationReminders = false;
                          } else {
                            // Optionally re-enable all if master is turned back on
                            // Or leave them as they were?
                            // Let's leave them as they were, user can re-enable individually
                          }
                        });
                        _updateSetting(_masterKey, value);
                        // Call LocalNotificationService
                        if (!value) {
                          LocalNotificationService.cancelAllReminders();
                        } else {
                          // If master is turned on, re-enable hydration if its toggle is on
                          if (_hydrationReminders) {
                            LocalNotificationService.scheduleHydrationReminders();
                          }
                          // Meal/Workout reminders are handled by their specific toggles
                          // and data loading, no need to schedule all here.
                        }
                      },
                      activeColor: _accentColor,
                    ),
                  ),
                ),

                _buildSectionHeader('Reminders'),
                Padding(
                  padding: _itemPadding,
                  child: Material(
                    color: _itemBackgroundColor,
                    borderRadius: _itemBorderRadius,
                    child: SwitchListTile(
                      contentPadding: _switchTilePadding,
                      title: Text('Workout Reminders', style: titleStyle),
                      subtitle: Text('Get notified 30 minutes before workouts.', style: subtitleStyle),
                      value: _workoutReminders,
                      onChanged: _masterNotifications
                          ? (bool value) {
                              setState(() {
                                _workoutReminders = value;
                              });
                              _updateSetting(_workoutKey, value);
                              // Call LocalNotificationService
                              if (!value) {
                                LocalNotificationService.cancelWorkoutReminders();
                              }
                              // No action needed to schedule when turning ON, happens with plan sync
                            }
                          : null, // Disable if master is off
                      activeColor: _accentColor,
                    ),
                  ),
                ),
                Padding(
                  padding: _itemPadding,
                  child: Material(
                    color: _itemBackgroundColor,
                    borderRadius: _itemBorderRadius,
                    child: SwitchListTile(
                      contentPadding: _switchTilePadding,
                      title: Text('Meal Reminders', style: titleStyle),
                      subtitle: Text('Get notified 15 minutes before meals.', style: subtitleStyle),
                      value: _mealReminders,
                      onChanged: _masterNotifications
                          ? (bool value) {
                              setState(() {
                                _mealReminders = value;
                              });
                               _updateSetting(_mealKey, value);
                               // Call LocalNotificationService
                              if (!value) {
                                LocalNotificationService.cancelMealReminders();
                              }
                              // No action needed to schedule when turning ON, happens with plan sync
                            }
                          : null,
                      activeColor: _accentColor,
                    ),
                  ),
                ),
                 Padding(
                  padding: _itemPadding,
                  child: Material(
                    color: _itemBackgroundColor,
                    borderRadius: _itemBorderRadius,
                    child: SwitchListTile(
                      contentPadding: _switchTilePadding,
                      title: Text('Hydration Reminders', style: titleStyle),
                      subtitle: Text('Receive periodic reminders to drink water.', style: subtitleStyle),
                      value: _hydrationReminders,
                      onChanged: _masterNotifications
                          ? (bool value) {
                              setState(() {
                                _hydrationReminders = value;
                              });
                              _updateSetting(_hydrationKey, value);
                              // Call LocalNotificationService
                              if (!value) {
                                LocalNotificationService.cancelHydrationReminders();
                              } else {
                                LocalNotificationService.scheduleHydrationReminders();
                              }
                            }
                          : null,
                      activeColor: _accentColor,
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  // Helper widget for section headers
  Widget _buildSectionHeader(String title) {
    return Padding(
      // Adjusted padding to align with Material items
      padding: const EdgeInsets.only(left: 20.0, right: 16.0, top: 24.0, bottom: 8.0),
      child: Text(
        title.toUpperCase(),
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600, // Slightly bolder
          color: _accentColor, // Use defined accent color
          letterSpacing: 1.1,
        ),
      ),
    );
  }
}
