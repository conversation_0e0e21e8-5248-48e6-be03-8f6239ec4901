from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from workouts.models import WorkoutPlan, WorkoutVideo
import os


class Command(BaseCommand):
    help = 'Add test videos to workout plans'

    def handle(self, *args, **options):
        try:
            # Get the first workout plan
            workout_plan = WorkoutPlan.objects.first()
            if not workout_plan:
                self.stdout.write(
                    self.style.ERROR('No workout plans found. Please create a workout plan first.')
                )
                return

            # Create test videos
            test_videos = [
                {
                    'title': 'Morning Warm-up',
                    'description': 'Start your day with this energizing warm-up routine',
                    'duration_seconds': 300,
                    'order': 1,
                },
                {
                    'title': 'Core Strength Training',
                    'description': 'Build your core strength with these targeted exercises',
                    'duration_seconds': 600,
                    'order': 2,
                },
                {
                    'title': 'Cool Down Stretches',
                    'description': 'Relax and stretch with this cool down routine',
                    'duration_seconds': 450,
                    'order': 3,
                },
            ]

            created_count = 0
            for video_data in test_videos:
                # Check if video already exists
                existing_video = WorkoutVideo.objects.filter(
                    workout_plan=workout_plan,
                    title=video_data['title']
                ).first()

                if not existing_video:
                    # Create the video
                    video = WorkoutVideo.objects.create(
                        workout_plan=workout_plan,
                        title=video_data['title'],
                        description=video_data['description'],
                        duration_seconds=video_data['duration_seconds'],
                        order=video_data['order'],
                    )
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'Created video: {video.title}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Video already exists: {video_data["title"]}')
                    )

            self.stdout.write(
                self.style.SUCCESS(f'Successfully created {created_count} test videos for workout plan: {workout_plan.name}')
            )

            # List all videos
            all_videos = WorkoutVideo.objects.all()
            self.stdout.write(f'\nTotal videos in database: {all_videos.count()}')
            for video in all_videos:
                self.stdout.write(f'- {video.title} (Plan: {video.workout_plan.name})')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating test videos: {str(e)}')
            )
