# Generated by Django 5.2.1 on 2025-05-31 19:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workouts', '0006_alter_workout_days_to_json'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkoutVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('video_file', models.FileField(help_text='Upload workout video file', upload_to='workout_videos/')),
                ('thumbnail', models.ImageField(blank=True, help_text='Upload video thumbnail', null=True, upload_to='workout_videos/thumbnails/')),
                ('duration_seconds', models.IntegerField(blank=True, help_text='Video duration in seconds', null=True)),
                ('order', models.IntegerField(default=1, help_text='Display order (priority)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('workout_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='videos', to='workouts.workoutplan')),
            ],
            options={
                'ordering': ['order', 'created_at'],
                'constraints': [models.UniqueConstraint(fields=('workout_plan', 'order'), name='unique_video_order_per_plan')],
            },
        ),
    ]
