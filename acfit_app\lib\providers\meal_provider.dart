import 'package:flutter/material.dart';
import 'dart:async'; // Import for StreamSubscription
import '../services/api_service.dart';
import '../services/notification_service.dart'; // Import for notifications
import '../models/meal_log.dart'; // Imports TodaysMealResponse, MealLog, Hydration
import '../models/meal.dart'; // Import MealDetail, MealPlan etc.
import 'package:intl/intl.dart'; // Import for date formatting
import '../utils/logger.dart'; // Import for logging
import '../utils/date_utils.dart' as app_date_utils; // Use alias
import 'package:timezone/timezone.dart' as tz; // Import tz
import 'auth_provider.dart'; // Import AuthProvider to access user timezone
import 'user_progress_provider.dart'; // Import UserProgressProvider

class MealProvider extends ChangeNotifier {
  final ApiService apiService;
  final AuthProvider authProvider; // Add AuthProvider dependency
  final UserProgressProvider
      userProgressProvider; // Add UserProgressProvider dependency
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _mealPlan;
  TodaysMealResponse? _todaysMeals; // State for today's meals
  // Add state for storing meal data for different dates
  final Map<DateTime, TodaysMealResponse?> _mealData = {};

  // Subscriptions for notifications
  StreamSubscription? _cacheSubscription;
  StreamSubscription? _mealCompletedSubscription;
  StreamSubscription? _mealLogUpdatedSubscription;
  StreamSubscription? _dayChangedSubscription; // Add subscription variable

  MealProvider({
    required this.apiService,
    required this.authProvider,
    required this.userProgressProvider,
  }) {
    // Listen for cache clearing notifications
    _cacheSubscription =
        NotificationService.instance.on('clear_meal_cache').listen((_) {
      // Clear meal cache notification received
      _clearCache();

      // Force a complete refresh of all meal data
      forceRefreshAllMealData();
    });

    // Also listen for meal completion notifications
    _mealCompletedSubscription =
        NotificationService.instance.on('meal_completed').listen((_) {
      // Meal completed notification received

      // Add a small delay to ensure the backend has processed the completion
      Future.delayed(const Duration(milliseconds: 500), () {
        forceRefreshAllMealData();
      });
    });

    // Also listen for meal log update notifications
    _mealLogUpdatedSubscription =
        NotificationService.instance.on('meal_log_updated').listen((_) {
      // Meal log updated notification received

      // Add a small delay to ensure the backend has processed the update
      Future.delayed(const Duration(milliseconds: 500), () {
        forceRefreshAllMealData();
      });
    });

    // Listen for user day change notifications
    _dayChangedSubscription =
        NotificationService.instance.on('user_day_changed').listen((_) {
      Logger.info(
          'User day changed notification received. Refreshing all meal data.',
          tag: 'MealProvider');
      forceRefreshAllMealData(); // Call the existing refresh method
    });
  }

  @override
  void dispose() {
    // Cancel all subscriptions when provider is disposed
    _cacheSubscription?.cancel();
    _mealCompletedSubscription?.cancel();
    _mealLogUpdatedSubscription?.cancel();
    _dayChangedSubscription?.cancel(); // Cancel day change subscription
    super.dispose();
  }

  // Clear the meal data cache
  void _clearCache() {
    // Clearing meal data cache
    _mealData.clear();
    _todaysMeals = null;
    notifyListeners();
  }

  // Helper to get current date in user's timezone
  tz.TZDateTime _getCurrentUserDate() {
    // Access timezone via authProvider.user.profile map
    final String? userTimezone =
        authProvider.user?.profile?['timezone'] as String?;
    final location = app_date_utils.getLocation(userTimezone);
    return app_date_utils.getCurrentDateInLocation(location);
  }

  // Force a complete refresh of all meal data
  Future<void> forceRefreshAllMealData() async {
    Logger.info('Starting complete meal data refresh', tag: 'MealProvider');
    _clearCache();

    try {
      // Refresh today's meals (using timezone-aware date)
      final today = _getCurrentUserDate();
      Logger.info('Loading meals for today: $today', tag: 'MealProvider');
      await fetchMealsForDate(today, forceRefresh: true);

      // Get location once for yesterday/tomorrow calculation
      final location = app_date_utils
          .getLocation(authProvider.user?.profile?['timezone'] as String?);
      final yesterday =
          tz.TZDateTime(location, today.year, today.month, today.day - 1);
      final tomorrow =
          tz.TZDateTime(location, today.year, today.month, today.day + 1);

      Logger.info('Fetching meals for yesterday: $yesterday',
          tag: 'MealProvider');
      await fetchMealsForDate(yesterday, forceRefresh: true);

      Logger.info('Fetching meals for tomorrow: $tomorrow',
          tag: 'MealProvider');
      await fetchMealsForDate(tomorrow, forceRefresh: true);

      Logger.info('All meal data refreshed successfully', tag: 'MealProvider');
    } catch (e) {
      Logger.error('Failed to refresh meal data: $e', tag: 'MealProvider');
    } finally {
      notifyListeners();
    }
  }

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic>? get mealPlan => _mealPlan;
  TodaysMealResponse? get todaysMeals => _todaysMeals;
  // Getter for meal data for specific dates
  Map<DateTime, TodaysMealResponse?> get mealData => _mealData;

  // Fetch meal data for a specific date and store it
  Future<void> fetchMealsForDate(DateTime date,
      {bool forceRefresh = false}) async {
    // Normalize the incoming date to the user's timezone date part
    final String? userTimezone =
        authProvider.user?.profile?['timezone'] as String?;
    final location = app_date_utils.getLocation(userTimezone);
    final dateKey = tz.TZDateTime(location, date.year, date.month, date.day);
    final dateStr =
        app_date_utils.formatDate(dateKey); // Format the timezone-aware date

    // Skip if already loading
    if (_isLoading) {
      return;
    }

    // Use cached data if available and not forcing refresh
    if (_mealData.containsKey(dateKey) && !forceRefresh) {
      return;
    }

    _isLoading = true;
    _error = null;

    try {
      Logger.info('Fetching meals for date $dateStr', tag: 'MealProvider');

      // Use date-based endpoint
      final response = await apiService.getMealsForDate(dateKey);

      Logger.info(
          'Fetched meal data for date $dateStr: Meals count = ${response.mealLogs.length}',
          tag: 'MealProvider');

      _mealData[dateKey] = response;

      // If this is today, also update todaysMeals
      final now = DateTime.now();
      final today = DateUtils.dateOnly(now);
      if (dateKey.year == today.year &&
          dateKey.month == today.month &&
          dateKey.day == today.day) {
        _todaysMeals = response;
      }
    } catch (e) {
      _error = 'Failed to load meals for $dateStr: ${e.toString()}';
      _mealData[dateKey] = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<void> loadMealPlan() async {
    _isLoading = true;
    _error = null;

    try {
      final response = await apiService.getUserMealPlans();

      if (response.isNotEmpty) {
        // Use the first meal plan in the list
        _mealPlan = Map<String, dynamic>.from(response[0]);
      } else {
        // Provide fallback data when no meal plans are available
        _mealPlan = {
          'id': 'fallback',
          'name': 'Default Meal Plan',
          'description': 'Please check back later for personalized meal plans',
          'duration_weeks': 4,
          'meals_per_day': 3
        };
      }
    } catch (e) {
      // Error loading meal plan
      // Provide fallback data in case of error
      _mealPlan = {
        'id': 'fallback',
        'name': 'Default Meal Plan',
        'description': 'Unable to load meal plan. Please try again later.',
        'duration_weeks': 4,
        'meals_per_day': 3
      };

      _error = 'Failed to load meal plans';
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify after loading finishes or fails
    }
  }

  // Mark a meal as complete and update the state
  Future<void> markMealComplete(int mealLogId, String dateStr) async {
    if (mealLogId <= 0) {
      Logger.error('Invalid meal log ID: $mealLogId', tag: 'MealProvider');
      return;
    }
    Logger.info('Marking meal complete: ID=$mealLogId, Date=$dateStr',
        tag: 'MealProvider');

    // Parse the date string to DateTime
    DateTime date;
    MealLog? logToUpdate; // Variable to hold the log object
    int baseCalories = 0; // Default base calories

    try {
      date = DateTime.parse(dateStr);
      // Find the log in local cache to get details needed for calorie calculation
      final dateKey = DateUtils.dateOnly(date);
      if (_mealData.containsKey(dateKey)) {
        var mealDataForDate = _mealData[dateKey];
        if (mealDataForDate != null) {
          // Use a sentinel MealLog for orElse to satisfy linter
          final sentinelMealLog = MealLog(
              id: -1,
              meal: MealDetail(id: -1, name: 'sentinel'),
              isCompleted: false);
          logToUpdate = mealDataForDate.mealLogs.firstWhere(
            (log) => log.id == mealLogId,
            orElse: () => sentinelMealLog, // Return sentinel
          );
          // Check if the result is the sentinel
          if (logToUpdate.id == sentinelMealLog.id) {
            logToUpdate = null; // Treat sentinel as not found
            Logger.warning(
                'Meal log ID $mealLogId not found in local cache for date $dateStr',
                tag: 'MealProvider');
          } else {
            // Log found, proceed to get base calories
            baseCalories = logToUpdate.meal.calories ?? 0;
            Logger.info('Found log in memory: baseCalories=$baseCalories',
                tag: 'MealProvider');
          }
        } // End: if (mealDataForDate != null)
      } // End: if (_mealData.containsKey(dateKey))
    } catch (e) {
      // If parsing fails or finding log fails, use today's date and defaults
      date = DateTime.now();
      Logger.error(
          'Failed to parse date or find log: $e, using today and defaults',
          tag: 'MealProvider');
    }

    // Set loading state
    _isLoading = true;
    notifyListeners(); // Notify UI that loading has started

    try {
      // --- BEGIN: Update actual calories before completing ---
      if (logToUpdate != null && baseCalories > 0) {
        // Calculate based on base calories only (assuming portion 1.0)
        final double calculatedCalories = baseCalories.toDouble();
        Logger.info(
            'Setting actual_calories: $calculatedCalories (from base meal calories)',
            tag: 'MealProvider');

        // Check if calculated calories are different from stored value
        // Use tolerance for double comparison
        final bool needsUpdate = (logToUpdate.actualCalories == null ||
            (logToUpdate.actualCalories! - calculatedCalories).abs() > 0.01);

        if (needsUpdate) {
          Logger.info(
              'Actual calories ($calculatedCalories) differs from stored (${logToUpdate.actualCalories}). Sending PATCH.',
              tag: 'MealProvider');
          await apiService.updateMealLog(mealLogId, {
            'actual_calories': calculatedCalories,
            // Removed portion_size from PATCH data
          });
          Logger.info('PATCH request sent successfully for meal log $mealLogId',
              tag: 'MealProvider');

          // Update local cache immediately after successful PATCH
          final dateKey = DateUtils.dateOnly(date);
          if (_mealData.containsKey(dateKey)) {
            var mealDataForDate = _mealData[dateKey];
            if (mealDataForDate != null) {
              for (int i = 0; i < mealDataForDate.mealLogs.length; i++) {
                if (mealDataForDate.mealLogs[i].id == mealLogId) {
                  mealDataForDate.mealLogs[i] =
                      mealDataForDate.mealLogs[i].copyWith(
                    actualCalories: calculatedCalories,
                    // Removed portion_size from copyWith
                  );
                  Logger.info(
                      'Local cache updated with calculated calories after PATCH.',
                      tag: 'MealProvider');
                  break;
                }
              }
            }
          }
          // Notify UI about the calorie update before completion UI update
          notifyListeners();
        } else {
          Logger.info(
              'Stored actual calories (${logToUpdate.actualCalories}) already match calculation. Skipping PATCH.',
              tag: 'MealProvider');
        }
      } else {
        Logger.warning(
            'Could not calculate actual calories (log not found or base calories zero). Skipping PATCH.',
            tag: 'MealProvider');
      }
      // --- END: Update actual calories before completing ---

      // Immediately update the UI to show completion (visual feedback first)
      final dateKey = DateUtils.dateOnly(date);
      if (_mealData.containsKey(dateKey)) {
        var mealDataForDate = _mealData[dateKey];
        if (mealDataForDate != null) {
          // Update the meal log in memory
          for (int i = 0; i < mealDataForDate.mealLogs.length; i++) {
            if (mealDataForDate.mealLogs[i].id == mealLogId) {
              Logger.info(
                  'Found meal log in memory, updating isCompleted to true',
                  tag: 'MealProvider');
              // Create a new copy with isCompleted set to true
              mealDataForDate.mealLogs[i] =
                  mealDataForDate.mealLogs[i].copyWith(
                isCompleted: true,
                completionTime: DateTime.now(),
              );
              break; // Exit loop once updated
            }
          }
        }
      }

      // Notify listeners to update UI immediately for completion status
      // This ensures the checkmark appears quickly
      notifyListeners();

      // Call the API to complete the meal log (POST request)
      Logger.info('Calling completeMealLog endpoint for ID=$mealLogId',
          tag: 'MealProvider');
      await apiService.completeMealLog(mealLogId: mealLogId);
      Logger.info('completeMealLog endpoint call successful for ID=$mealLogId',
          tag: 'MealProvider');

      // Force refresh calorie data immediately after completion
      try {
        await apiService.getCalories(skipCache: true);
        Logger.info('Successfully refreshed calorie data after meal completion',
            tag: 'MealProvider');
      } catch (e) {
        Logger.error('Failed to refresh calorie data after meal completion: $e',
            tag: 'MealProvider');
      }

      // Add a small delay to ensure the backend has processed the completion
      await Future.delayed(const Duration(milliseconds: 500));

      // Force a complete refresh of all meal data from the server
      // This ensures consistency after both PATCH and POST calls
      await forceRefreshAllMealData();

      Logger.info('Successfully refreshed meal data after completion',
          tag: 'MealProvider');
    } catch (e, stackTrace) {
      // Catch stack trace
      Logger.error('Failed operation in markMealComplete: $e',
          tag: 'MealProvider', stackTrace: stackTrace); // Log stack trace

      // Even if there's an error, try to refresh the data one more time
      try {
        await Future.delayed(const Duration(milliseconds: 1000));
        await forceRefreshAllMealData();
      } catch (e2, stackTrace2) {
        // Catch stack trace
        Logger.error('Failed final attempt to refresh meal data: $e2',
            tag: 'MealProvider', stackTrace: stackTrace2); // Log stack trace
      }
    } finally {
      // Reset loading state
      _isLoading = false;

      // Notify listeners again after all updates
      notifyListeners();
    }
  }

  // Update hydration
  Future<void> updateHydration(int amountMl) async {
    if (amountMl <= 0) {
      Logger.error('Invalid hydration amount: $amountMl', tag: 'MealProvider');
      return;
    }

    Logger.info('Updating hydration: $amountMl ml', tag: 'MealProvider');

    // Set loading state
    _isLoading = true;
    notifyListeners();

    try {
      // Call the API to log hydration
      await apiService.logHydration(amountMl: amountMl);

      // Add a small delay to ensure the backend has processed the update
      await Future.delayed(const Duration(milliseconds: 500));

      // Force a refresh of today's meal data to get updated hydration
      final today = _getCurrentUserDate();
      await fetchMealsForDate(today, forceRefresh: true);

      Logger.info('Successfully updated hydration', tag: 'MealProvider');
    } catch (e) {
      Logger.error('Failed to update hydration: $e', tag: 'MealProvider');
      _error = 'Failed to update hydration: ${e.toString()}';
    } finally {
      // Reset loading state
      _isLoading = false;

      // Notify listeners
      notifyListeners();
    }
  }
}
