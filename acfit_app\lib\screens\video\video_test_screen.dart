import 'package:flutter/material.dart';
import '../../models/workout_video.dart';
import 'workout_videos_screen.dart';

class VideoTestScreen extends StatelessWidget {
  const VideoTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Create mock video data for testing
    final mockVideos = [
      WorkoutVideo(
        id: 1,
        title: "Morning Warm-up",
        description: "Start your day with this energizing warm-up routine",
        videoUrl: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
        thumbnailUrl: "https://via.placeholder.com/300x200/FF6B35/FFFFFF?text=Warm-up",
        durationSeconds: 300,
        order: 1,
        createdAt: "2024-01-01T10:00:00Z",
      ),
      WorkoutVideo(
        id: 2,
        title: "Core Strength",
        description: "Build your core strength with these targeted exercises",
        videoUrl: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4",
        thumbnailUrl: "https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=Core",
        durationSeconds: 600,
        order: 2,
        createdAt: "2024-01-01T11:00:00Z",
      ),
      WorkoutVideo(
        id: 3,
        title: "Cool Down",
        description: "Relax and stretch with this cool down routine",
        videoUrl: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4",
        thumbnailUrl: "https://via.placeholder.com/300x200/45B7D1/FFFFFF?text=Cool+Down",
        durationSeconds: 450,
        order: 3,
        createdAt: "2024-01-01T12:00:00Z",
      ),
    ];

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Video Test Screen',
          style: TextStyle(
            color: Color(0xFF101114),
            fontSize: 20,
            fontWeight: FontWeight.w600,
            fontFamily: 'Work Sans',
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF101114)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Video Functionality',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF101114),
                fontFamily: 'Work Sans',
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This screen tests the video functionality with mock data. Click the button below to see the video list screen.',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF6B7280),
                fontFamily: 'Work Sans',
              ),
            ),
            const SizedBox(height: 24),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Mock Video Data:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF101114),
                      fontFamily: 'Work Sans',
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...mockVideos.map((video) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Color(0xFFF97316),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            '${video.title} (${video.formattedDuration})',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF6B7280),
                              fontFamily: 'Work Sans',
                            ),
                          ),
                        ),
                      ],
                    ),
                  )).toList(),
                ],
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Navigate to video list screen with mock data
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WorkoutVideosScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFF97316),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Test Video List Screen',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Work Sans',
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Note: The video list screen will show "No videos available" because it fetches from the API. This test screen just demonstrates the UI components.',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF9CA3AF),
                fontFamily: 'Work Sans',
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
