
/// Utility class for converting between different volume units
class UnitConverter {
  // Conversion constants
  static const double _ML_PER_OZ = 29.5735;
  static const double _ML_PER_CUP = 236.588;
  static const double _ML_PER_PINT = 473.176;
  static const double _ML_PER_QUART = 946.353;
  static const double _ML_PER_GALLON = 3785.41;

  // Convert from other units to milliliters
  static int ozToMl(double oz) => (oz * _ML_PER_OZ).round();
  static int cupsToMl(double cups) => (cups * _ML_PER_CUP).round();
  static int pintsToMl(double pints) => (pints * _ML_PER_PINT).round();
  static int quartsToMl(double quarts) => (quarts * _ML_PER_QUART).round();
  static int gallonsToMl(double gallons) => (gallons * _ML_PER_GALLON).round();

  // Convert from milliliters to other units
  static double mlToOz(int ml) => ml / _ML_PER_OZ;
  static double mlToCups(int ml) => ml / _ML_PER_CUP;
  static double mlToPints(int ml) => ml / _ML_PER_PINT;
  static double mlToQuarts(int ml) => ml / _ML_PER_QUART;
  static double mlToGallons(int ml) => ml / _ML_PER_GALLON;

  // Format the value with appropriate precision
  static String formatVolume(double value, {int precision = 1}) {
    if (value == value.roundToDouble()) {
      return value.toInt().toString();
    }
    return value.toStringAsFixed(precision);
  }
}

/// Container types with their standard volumes in ml
class HydrationContainer {
  final String name;
  final int volumeMl;
  final String iconPath; // Path to the icon asset

  const HydrationContainer({
    required this.name,
    required this.volumeMl,
    required this.iconPath,
  });
}

/// Standard container definitions
class HydrationContainers {
  static const HydrationContainer smallGlass = HydrationContainer(
    name: 'Small Glass',
    volumeMl: 150,
    iconPath: 'assets/icons/small_glass.png',
  );

  static const HydrationContainer standardGlass = HydrationContainer(
    name: 'Glass',
    volumeMl: 250,
    iconPath: 'assets/icons/glass.png',
  );

  static const HydrationContainer cup = HydrationContainer(
    name: 'Cup',
    volumeMl: 237,
    iconPath: 'assets/icons/cup.png',
  );

  static const HydrationContainer mug = HydrationContainer(
    name: 'Mug',
    volumeMl: 350,
    iconPath: 'assets/icons/mug.png',
  );

  static const HydrationContainer smallBottle = HydrationContainer(
    name: 'Small Bottle',
    volumeMl: 500,
    iconPath: 'assets/icons/small_bottle.png',
  );

  static const HydrationContainer largeBottle = HydrationContainer(
    name: 'Large Bottle',
    volumeMl: 750,
    iconPath: 'assets/icons/large_bottle.png',
  );

  static const HydrationContainer waterBottle = HydrationContainer(
    name: 'Water Bottle',
    volumeMl: 1000,
    iconPath: 'assets/icons/water_bottle.png',
  );

  // List of all standard containers
  static const List<HydrationContainer> all = [
    smallGlass,
    standardGlass,
    cup,
    mug,
    smallBottle,
    largeBottle,
    waterBottle,
  ];
}
