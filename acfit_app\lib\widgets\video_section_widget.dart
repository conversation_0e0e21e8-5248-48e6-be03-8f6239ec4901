import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/workout_video.dart';
import '../screens/video/workout_videos_screen.dart';
import '../screens/video/video_player_screen.dart';

class VideoSectionWidget extends StatelessWidget {
  final List<WorkoutVideo>? videos;
  final bool isLoading;

  const VideoSectionWidget({
    Key? key,
    this.videos,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Workout Videos',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF101114),
                  fontFamily: 'Work Sans',
                ),
              ),
              if (videos != null && videos!.isNotEmpty)
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WorkoutVideosScreen(),
                      ),
                    );
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFF97316),
                      fontFamily: 'Work Sans',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          _buildVideoContent(context),
        ],
      ),
    );
  }

  Widget _buildVideoContent(BuildContext context) {
    if (isLoading) {
      return Container(
        width: double.infinity,
        height: 200,
        padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          color: const Color(0xFF1A1B1F),
        ),
        child: const Center(
          child: CircularProgressIndicator(
            color: Color(0xFFF97316),
          ),
        ),
      );
    }

    if (videos == null || videos!.isEmpty) {
      return Container(
        width: double.infinity,
        height: 200,
        padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          color: const Color(0xFF1A1B1F),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              color: Colors.white54,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'No videos for you at the moment',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
                fontFamily: 'Work Sans',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Show video thumbnails in horizontal scroll
    return SizedBox(
      height: 200,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: videos!
              .take(3) // Show max 3 videos in home screen
              .map((video) => _buildVideoCard(context, video))
              .toList()
              .expand((widget) => [widget, const SizedBox(width: 16)])
              .toList()
            ..removeLast(), // Remove last spacer
        ),
      ),
    );
  }

  Widget _buildVideoCard(BuildContext context, WorkoutVideo video) {
    return GestureDetector(
      onTap: () {
        // Check if video has a valid video URL
        if (video.videoUrl == null || video.videoUrl!.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Video not available'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => VideoPlayerScreen(video: video),
          ),
        );
      },
      child: Container(
        width: 280,
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: const Color(0xFF1A1B1F),
        ),
        child: Stack(
          children: [
            // Video thumbnail
            ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: const Color(0xFF2A2B2F),
                child:
                    video.thumbnailUrl != null && video.thumbnailUrl!.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: video.thumbnailUrl!,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => const Center(
                              child: CircularProgressIndicator(
                                color: Color(0xFFF97316),
                              ),
                            ),
                            errorWidget: (context, url, error) => const Center(
                              child: Icon(
                                Icons.video_library_outlined,
                                color: Colors.white54,
                                size: 48,
                              ),
                            ),
                          )
                        : const Center(
                            child: Icon(
                              Icons.video_library_outlined,
                              color: Colors.white54,
                              size: 48,
                            ),
                          ),
              ),
            ),
            // Play button overlay
            Center(
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Color(0xFFF97316),
                  size: 32,
                ),
              ),
            ),
            // Video info overlay
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.8),
                    ],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      video.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        fontFamily: 'Work Sans',
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (video.durationSeconds != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        video.formattedDuration,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white70,
                          fontFamily: 'Work Sans',
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
