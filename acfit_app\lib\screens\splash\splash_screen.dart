import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import '../../services/navigation_service.dart';
import '../main/main_screen.dart'; // Import MainScreen

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  // Renamed state class
  SplashScreenState createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // Animation controllers
  late AnimationController _animationController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _setupAnimations();

    // Start authentication check after a short delay to allow animations to play
    Future.delayed(const Duration(milliseconds: 1500), _checkAuthStatus);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    // Create animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Logo scale animation - starts small and bounces to full size
    _logoScaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.0, end: 1.2).chain(
          CurveTween(curve: Curves.easeOutQuad),
        ),
        weight: 60.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0).chain(
          CurveTween(curve: Curves.elasticOut),
        ),
        weight: 40.0,
      ),
    ]).animate(_animationController);

    // Text fade-in animation - starts after logo animation begins
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.4, 0.8, curve: Curves.easeIn),
    ));

    // Subtle pulse animation for the logo after initial animation
    _pulseAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.05),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.05, end: 1.0),
        weight: 50,
      ),
    ]).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.8, 1.0, curve: Curves.easeInOut),
    ));

    // Start the animation
    _animationController.forward();
  }

  Future<void> _checkAuthStatus() async {
    // Use a post-frame callback to safely access the provider after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Check if the widget is still mounted before proceeding
      if (!mounted) return;

      try {
        final authService = Provider.of<AuthService>(context, listen: false);

        // Check auth status and update provider state
        await authService.checkAuthStatus();
        final isLoggedIn = authService.isAuthenticated;

        // Check mount status again before navigation
        if (!mounted) return;

        if (isLoggedIn) {
          // Verify token validity by trying to fetch profile
          try {
            await authService.getUserProfile();

            // Navigate to main screen if profile fetch succeeds
            if (!mounted) return;
            // Use pushAndRemoveUntil to clear the navigation stack
            // This prevents going back to welcome/login screens after authentication
            NavigationService.navigatorKey.currentState?.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const MainScreen()),
              (Route<dynamic> route) => false,
            );
          } catch (profileError) {
            // If profile fetch fails, token might be invalid, log out
            if (!mounted) return;
            await authService.logout();
            if (!mounted) return;
            await NavigationService.navigateToReplacementNamed('/welcome');
          }
        } else {
          // User is not logged in
          await NavigationService.navigateToReplacementNamed('/welcome');
        }
      } catch (e) {
        // Catch potential errors during provider access or navigation
        if (mounted) {
          await NavigationService.navigateToReplacementNamed('/welcome');
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: const Color(0xFFF97316), // App's primary orange color
        child: Center(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Animated Logo
                  Transform.scale(
                    scale: _logoScaleAnimation.value * _pulseAnimation.value,
                    child: Container(
                      width: 192,
                      height: 192,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage('assets/images/logo.png'),
                          fit: BoxFit.fitWidth,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // Animated App Name
                  Opacity(
                    opacity: _fadeAnimation.value,
                    child: const Text(
                      'AC Fit',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Work Sans',
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 13),
                  // Animated Tagline
                  Opacity(
                    opacity: _fadeAnimation.value,
                    child: const Text(
                      'Your REAL Fitness Partner',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Work Sans',
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                        height: 1.5,
                      ),
                    ),
                  ),
                  const SizedBox(height: 30),
                  // Custom animated loading indicator
                  Opacity(
                    opacity: _fadeAnimation.value,
                    child: _buildCustomLoadingIndicator(),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCustomLoadingIndicator() {
    return SizedBox(
      width: 50,
      height: 50,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer circle
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
              shape: BoxShape.circle,
            ),
          ),
          // Rotating progress indicator
          RotationTransition(
            turns: _animationController,
            child: CircularProgressIndicator(
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
              backgroundColor: Colors.white.withAlpha(77), // 0.3 * 255 = 77
              strokeWidth: 3,
            ),
          ),
          // Center icon
          const Icon(
            Icons.fitness_center,
            color: Colors.white,
            size: 24,
          ),
        ],
      ),
    );
  }
}
