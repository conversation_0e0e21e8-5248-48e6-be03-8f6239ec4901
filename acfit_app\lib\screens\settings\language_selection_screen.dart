import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../generated/l10n.dart';

class LanguageSelectionScreen extends StatefulWidget {
  const LanguageSelectionScreen({Key? key}) : super(key: key);

  @override
  State<LanguageSelectionScreen> createState() => _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  late String _selectedLanguageCode;

  @override
  void initState() {
    super.initState();
    // Initialize with the current language
    _selectedLanguageCode = Provider.of<LanguageProvider>(context, listen: false).locale.languageCode;
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Scaffold(
      backgroundColor: const Color.fromRGBO(16, 17, 20, 1), // Dark background for AppBar area
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            backgroundColor: const Color.fromRGBO(16, 17, 20, 1), // Dark background
            pinned: true, // Keep AppBar visible
            expandedHeight: 120.0, // Adjust as needed
            leading: Padding(
              padding: const EdgeInsets.only(left: 16.0, top: 10.0), // Adjust padding
              child: Container(
                width: 40, // Explicit width
                height: 40, // Explicit height
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(35, 37, 43, 1), // Button background
                  borderRadius: BorderRadius.circular(12), // Rounded corners
                ),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 20),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: const EdgeInsets.only(left: 16.0, bottom: 16.0), // Adjust padding
              title: Text(
                AppLocalizations.of(context).languageSelectionTitle,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Work Sans',
                  fontSize: 24, // Slightly smaller size for AppBar
                  fontWeight: FontWeight.w600, // Bolder
                ),
              ),
              background: Container(color: const Color.fromRGBO(16, 17, 20, 1)), // Ensure background is dark
            ),
          ),
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              decoration: const BoxDecoration(
                color: Colors.white, // White background for the list content
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30), // Rounded corners for the content area
                  topRight: Radius.circular(30),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const SizedBox(height: 24),
                  // Build language selection list
                  ...languageProvider.supportedLanguages.entries.map((entry) {
                    final languageCode = entry.key;
                    final languageName = entry.value;

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Material(
                        color: _selectedLanguageCode == languageCode
                            ? const Color.fromRGBO(255, 236, 228, 1) // Selected background
                            : const Color.fromRGBO(243, 243, 243, 1), // Default background
                        borderRadius: BorderRadius.circular(24),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(24),
                          onTap: () {
                            setState(() {
                              _selectedLanguageCode = languageCode;
                            });
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    languageName,
                                    style: TextStyle(
                                      color: _selectedLanguageCode == languageCode
                                          ? const Color.fromRGBO(255, 127, 54, 1) // Selected text color
                                          : const Color.fromRGBO(57, 59, 66, 1), // Default text color
                                      fontFamily: 'Work Sans',
                                      fontSize: 16,
                                      fontWeight: _selectedLanguageCode == languageCode
                                          ? FontWeight.w600 // Bold for selected
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ),
                                if (_selectedLanguageCode == languageCode)
                                  const Icon(
                                    Icons.check_circle,
                                    color: Color.fromRGBO(255, 127, 54, 1), // Orange check icon
                                    size: 24,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),

                  const SizedBox(height: 32),

                  // Save button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        // Save the selected language
                        await languageProvider.setLanguage(_selectedLanguageCode);

                        if (context.mounted) {
                          // Show a snackbar to confirm the change
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(AppLocalizations.of(context).languageChangedMessage(languageProvider.currentLanguageName)),
                              duration: const Duration(seconds: 2),
                            ),
                          );

                          // Go back to the settings screen
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color.fromRGBO(255, 127, 54, 1), // Orange button
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context).saveButton,
                        style: const TextStyle(
                          fontFamily: 'Work Sans',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
