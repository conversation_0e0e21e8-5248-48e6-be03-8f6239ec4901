# Generated by Django 5.2.1 on 2025-05-31 17:35

from django.db import migrations, models
import json


def convert_workout_days_to_json(apps, schema_editor):
    """Convert string workout_days values to JSON format"""
    WorkoutPlan = apps.get_model('workouts', 'WorkoutPlan')

    for plan in WorkoutPlan.objects.all():
        if plan.workout_days and isinstance(plan.workout_days, str):
            # Convert single string to JSON list
            plan.workout_days = json.dumps([plan.workout_days])
            plan.save(update_fields=['workout_days'])


def reverse_workout_days_conversion(apps, schema_editor):
    """Reverse the conversion - convert JSON back to string"""
    WorkoutPlan = apps.get_model('workouts', 'WorkoutPlan')

    for plan in WorkoutPlan.objects.all():
        if plan.workout_days:
            try:
                # Try to parse as JSON and convert back to string
                parsed = json.loads(plan.workout_days)
                if isinstance(parsed, list) and len(parsed) > 0:
                    plan.workout_days = str(parsed[0])  # Take first item
                    plan.save(update_fields=['workout_days'])
            except (json.JSONDecodeError, TypeError):
                # If it's already a string, leave it as is
                pass


class Migration(migrations.Migration):

    dependencies = [
        ('workouts', '0004_alter_workoutplan_age_group'),
    ]

    operations = [
        # First, temporarily increase the max_length to allow JSON strings
        migrations.AlterField(
            model_name='workoutplan',
            name='workout_days',
            field=models.CharField(max_length=200, choices=[], default='2_3_DAYS'),
        ),
        # Then convert the data
        migrations.RunPython(convert_workout_days_to_json, reverse_workout_days_conversion),
    ]
