# Generated by Django 5.2.1 on 2025-05-31 17:31

from django.db import migrations
import json


def convert_age_group_to_json(apps, schema_editor):
    """Convert string age_group values to JSON format"""
    WorkoutPlan = apps.get_model('workouts', 'WorkoutPlan')

    for plan in WorkoutPlan.objects.all():
        if plan.age_group and isinstance(plan.age_group, str):
            # Convert string to JSON format
            plan.age_group = json.dumps(plan.age_group)
            plan.save(update_fields=['age_group'])


def reverse_age_group_conversion(apps, schema_editor):
    """Reverse the conversion - convert JSON back to string"""
    WorkoutPlan = apps.get_model('workouts', 'WorkoutPlan')

    for plan in WorkoutPlan.objects.all():
        if plan.age_group:
            try:
                # Try to parse as JSON and convert back to string
                parsed = json.loads(plan.age_group)
                plan.age_group = str(parsed)
                plan.save(update_fields=['age_group'])
            except (json.JSONDecodeError, TypeError):
                # If it's already a string, leave it as is
                pass


class Migration(migrations.Migration):

    dependencies = [
        ('workouts', '0003_remove_exercise_image_url_remove_exercise_video_url_and_more'),
    ]

    operations = [
        migrations.RunPython(convert_age_group_to_json, reverse_age_group_conversion),
    ]
