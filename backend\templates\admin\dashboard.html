{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" href="{% static 'admin/css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="dashboard-container">
  <div class="dashboard-header">
    <h1>AC-FIT Admin Dashboard</h1>
  </div>

  <div class="dashboard-stats">
    <div class="stat-card">
      <h3>Users</h3>
      <div class="stat-value">{{ user_count }}</div>
    </div>
    <div class="stat-card">
      <h3>Workout Plans</h3>
      <div class="stat-value">{{ workout_plan_count }}</div>
    </div>
    <div class="stat-card">
      <h3>Meal Plans</h3>
      <div class="stat-value">{{ meal_plan_count }}</div>
    </div>
    <div class="stat-card">
      <h3>Active Workout Plans</h3>
      <div class="stat-value">{{ active_workout_plans }}</div>
    </div>
    <div class="stat-card">
      <h3>Active Meal Plans</h3>
      <div class="stat-value">{{ active_meal_plans }}</div>
    </div>
    <div class="stat-card">
      <h3>Activity Logs</h3>
      <div class="stat-value">{{ activity_log_count }}</div>
    </div>
  </div>

  <div class="dashboard-actions">
    <a href="{% url 'acfit_admin:assign-plans' %}" class="action-button">
      <i class="fas fa-user-plus"></i> Assign Plans to Users
    </a>
    <a href="{% url 'acfit_admin:questionnaire-manager' %}" class="action-button">
      <i class="fas fa-question-circle"></i> Manage Questionnaire
    </a>
    <a href="{% url 'acfit_admin:products-dashboard' %}" class="action-button">
      <i class="fas fa-shopping-cart"></i> Manage Products
    </a>
    <a href="{% url 'acfit_admin:faqs' %}" class="action-button">
      <i class="fas fa-info-circle"></i> Manage FAQs
    </a>
    <a href="{% url 'acfit_admin:feedback' %}" class="action-button">
      <i class="fas fa-comments"></i> User Feedback
    </a>
    <a href="/admin/activity_logs/activitylog/" class="action-button">
      <i class="fas fa-history"></i> Activity Logs
    </a>
    <a href="{% url 'acfit_admin:workout-calendar' %}" class="action-button">
      <i class="fas fa-calendar-alt"></i> Workout Calendar
    </a>
    <a href="{% url 'acfit_admin:program-days' %}" class="action-button">
      <i class="fas fa-list-ol"></i> Program Days
    </a>
    <a href="/acfit-admin/workouts/workoutvideo/" class="action-button">
      <i class="fas fa-video"></i> Workout Videos
    </a>
    <a href="{% url 'acfit_admin:meal-calendar' %}" class="action-button">
      <i class="fas fa-utensils"></i> Meal Calendar
    </a>
    <a href="{% url 'acfit_admin:documentation' %}" class="action-button">
      <i class="fas fa-book"></i> Admin Documentation
    </a>
  </div>

  <div class="dashboard-sections">
    <div class="dashboard-section">
      <h2>Recent Users</h2>
      <table>
        <thead>
          <tr>
            <th>Username</th>
            <th>Email</th>
            <th>Date Joined</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for profile in recent_users %}
          <tr>
            <td>{{ profile.user.username }}</td>
            <td>{{ profile.user.email }}</td>
            <td>{{ profile.user.date_joined|date }}</td>
            <td>
              <a href="{% url 'acfit_admin:edit-user' profile.user.id %}" class="button">View</a>
              <a href="{% url 'acfit_admin:delete-user' profile.user.id %}" class="button" style="background-color: #EF4444;">Delete</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4">No users found.</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      <div class="dashboard-actions">
        <a href="{% url 'acfit_admin:users' %}" class="view-all">View All Users</a>
        <a href="{% url 'acfit_admin:add-user' %}" class="button">Add New User</a>
      </div>
    </div>

    <div class="dashboard-section">
      <h2>Recent Feedback</h2>
      <table>
        <thead>
          <tr>
            <th>Subject</th>
            <th>User</th>
            <th>Type</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for feedback in recent_feedback %}
          <tr>
            <td>{{ feedback.subject }}</td>
            <td>{{ feedback.user.username }}</td>
            <td>{{ feedback.get_feedback_type_display }}</td>
            <td>{% if feedback.is_resolved %}Resolved{% else %}<span style="color: #EF4444;">Unresolved</span>{% endif %}</td>
            <td>
              <a href="{% url 'acfit_admin:view-feedback' feedback.id %}" class="button">View</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="5">No feedback found.</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      <div class="dashboard-actions">
        <a href="{% url 'acfit_admin:feedback' %}" class="view-all">View All Feedback</a>
      </div>
    </div>

    <div class="dashboard-section">
      <h2>Recent Workout Plans</h2>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Fitness Level</th>
            <th>Created</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for plan in recent_workout_plans %}
          <tr>
            <td>{{ plan.name }}</td>
            <td>{{ plan.get_fitness_level_display }}</td>
            <td>{{ plan.created_at|date }}</td>
            <td>
              <a href="{% url 'acfit_admin:edit-workout-plan' plan.id %}" class="button">Edit</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4">No workout plans found.</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      <div class="dashboard-actions">
        <a href="{% url 'acfit_admin:workout-plans' %}" class="view-all">View All Workout Plans</a>
        <a href="{% url 'acfit_admin:add-workout-plan' %}" class="button">Add New Workout Plan</a>
      </div>
    </div>

    <div class="dashboard-section">
      <h2>Recent Meal Plans</h2>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Daily Calories</th>
            <th>Created</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for plan in recent_meal_plans %}
          <tr>
            <td>{{ plan.name }}</td>
            <td>{{ plan.daily_calories }}</td>
            <td>{{ plan.created_at|date }}</td>
            <td>
              <a href="{% url 'acfit_admin:edit-meal-plan' plan.id %}" class="button">Edit</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4">No meal plans found.</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      <div class="dashboard-actions">
        <a href="{% url 'acfit_admin:meal-plans' %}" class="view-all">View All Meal Plans</a>
        <a href="{% url 'acfit_admin:add-meal-plan' %}" class="button">Add New Meal Plan</a>
      </div>
    </div>

    <div class="dashboard-section">
      <h2>Recent Activity Logs</h2>
      <table>
        <thead>
          <tr>
            <th>User</th>
            <th>Activity</th>
            <th>Date</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for log in recent_activity_logs %}
          <tr>
            <td>{{ log.user.user.username }}</td>
            <td>{{ log.get_activity_type_display }}</td>
            <td>{{ log.timestamp|date }} {{ log.timestamp|time }}</td>
            <td>
              <a href="/admin/activity_logs/activitylog/{{ log.id }}/change/" class="button">View</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4">No activity logs found.</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      <div class="dashboard-actions">
        <a href="/admin/activity_logs/activitylog/" class="view-all">View All Activity Logs</a>
      </div>
    </div>
  </div>
</div>
{% endblock %}
