{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" href="{% static 'admin/css/dashboard.css' %}">
  <style>
    .documentation-container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .doc-header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #417690;
    }
    
    .doc-header h1 {
      color: #417690;
      font-size: 2.5em;
      margin-bottom: 10px;
    }
    
    .doc-header p {
      color: #666;
      font-size: 1.2em;
    }
    
    .doc-nav {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
    }
    
    .doc-nav h2 {
      color: #417690;
      margin-bottom: 15px;
    }
    
    .doc-nav ul {
      list-style: none;
      padding: 0;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 10px;
    }
    
    .doc-nav li {
      margin: 0;
    }
    
    .doc-nav a {
      display: block;
      padding: 10px 15px;
      background: #fff;
      color: #417690;
      text-decoration: none;
      border-radius: 4px;
      border: 1px solid #ddd;
      transition: all 0.3s ease;
    }
    
    .doc-nav a:hover {
      background: #417690;
      color: #fff;
      transform: translateY(-2px);
    }
    
    .doc-section {
      margin-bottom: 40px;
      padding: 30px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #417690;
    }
    
    .doc-section h2 {
      color: #417690;
      font-size: 1.8em;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }
    
    .doc-section h3 {
      color: #333;
      font-size: 1.4em;
      margin: 25px 0 15px 0;
    }
    
    .doc-section h4 {
      color: #555;
      font-size: 1.2em;
      margin: 20px 0 10px 0;
    }
    
    .doc-section p {
      line-height: 1.6;
      margin-bottom: 15px;
      color: #333;
    }
    
    .doc-section ul, .doc-section ol {
      margin-bottom: 15px;
      padding-left: 30px;
    }
    
    .doc-section li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    
    .feature-card {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #ddd;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .feature-card h4 {
      color: #417690;
      margin-bottom: 10px;
    }
    
    .code-block {
      background: #2d3748;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      margin: 15px 0;
      overflow-x: auto;
    }
    
    .warning-box {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-left: 4px solid #f39c12;
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
    }
    
    .warning-box h4 {
      color: #e67e22;
      margin-bottom: 10px;
    }
    
    .info-box {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      border-left: 4px solid #17a2b8;
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
    }
    
    .info-box h4 {
      color: #138496;
      margin-bottom: 10px;
    }
    
    .success-box {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-left: 4px solid #28a745;
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
    }
    
    .success-box h4 {
      color: #155724;
      margin-bottom: 10px;
    }
    
    .step-list {
      counter-reset: step-counter;
    }
    
    .step-list li {
      counter-increment: step-counter;
      position: relative;
      padding-left: 40px;
      margin-bottom: 20px;
    }
    
    .step-list li::before {
      content: counter(step-counter);
      position: absolute;
      left: 0;
      top: 0;
      background: #417690;
      color: white;
      width: 25px;
      height: 25px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
    }
    
    .back-to-top {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #417690;
      color: white;
      padding: 10px 15px;
      border-radius: 50px;
      text-decoration: none;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
    }
    
    .back-to-top:hover {
      background: #2c5282;
      transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
      .documentation-container {
        margin: 10px;
        padding: 15px;
      }
      
      .doc-nav ul {
        grid-template-columns: 1fr;
      }
      
      .feature-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
{% endblock %}

{% block content %}
<div class="documentation-container">
  <div class="doc-header">
    <h1>🏋️ AC-FIT Admin Documentation</h1>
    <p>Complete guide to managing your fitness platform</p>
  </div>

  <div class="doc-nav">
    <h2>📋 Quick Navigation</h2>
    <ul>
      <li><a href="#overview">🏠 Dashboard Overview</a></li>
      <li><a href="#users">👥 User Management</a></li>
      <li><a href="#workouts">💪 Workout Management</a></li>
      <li><a href="#meals">🍽️ Meal Management</a></li>
      <li><a href="#videos">🎥 Video Management</a></li>
      <li><a href="#questionnaire">❓ Questionnaire Management</a></li>
      <li><a href="#products">🛒 Product Management</a></li>
      <li><a href="#support">🎧 Support Management</a></li>
      <li><a href="#activity">📊 Activity Logs</a></li>
      <li><a href="#assignment">📝 Plan Assignment</a></li>
      <li><a href="#calendar">📅 Calendar Views</a></li>
      <li><a href="#security">🔒 Security & Permissions</a></li>
    </ul>
  </div>

  <div id="overview" class="doc-section">
    <h2>🏠 Dashboard Overview</h2>
    <p>The AC-FIT admin dashboard is your central hub for managing the entire fitness platform. Here's what you can access:</p>
    
    <div class="feature-grid">
      <div class="feature-card">
        <h4>📊 Key Metrics</h4>
        <p>View total users, active workout plans, meal plans, and recent activity at a glance.</p>
      </div>
      <div class="feature-card">
        <h4>🚀 Quick Actions</h4>
        <p>Access frequently used functions like assigning plans, managing questionnaires, and viewing feedback.</p>
      </div>
      <div class="feature-card">
        <h4>📈 Recent Activity</h4>
        <p>Monitor recent user registrations, feedback submissions, and system activity.</p>
      </div>
      <div class="feature-card">
        <h4>🔗 Direct Links</h4>
        <p>Quick access to all major admin functions through the action buttons.</p>
      </div>
    </div>

    <div class="info-box">
      <h4>💡 Pro Tip</h4>
      <p>The dashboard automatically refreshes key metrics. Use the action buttons for quick access to common tasks.</p>
    </div>
  </div>

  <div id="users" class="doc-section">
    <h2>👥 User Management</h2>
    <p>Comprehensive user management system for handling user accounts, profiles, progress tracking, and scoring.</p>
    
    <h3>User Accounts</h3>
    <p>Manage user authentication and basic account information:</p>
    <ul>
      <li><strong>View Users:</strong> Access complete user list with search and filtering</li>
      <li><strong>Edit Users:</strong> Modify user details, permissions, and status</li>
      <li><strong>Delete Users:</strong> Remove user accounts (with confirmation)</li>
      <li><strong>User Status:</strong> Activate/deactivate accounts</li>
    </ul>

    <h3>User Profiles</h3>
    <p>Detailed user profile management including:</p>
    <ul>
      <li><strong>Personal Info:</strong> Age, gender, fitness goals, experience level</li>
      <li><strong>Health Conditions:</strong> Medical considerations for plan assignment</li>
      <li><strong>Preferences:</strong> Dietary preferences (keto, intermittent fasting)</li>
      <li><strong>Active Plans:</strong> Current workout and meal plan assignments</li>
    </ul>

    <h3>Progress Tracking</h3>
    <p>Monitor user progress across multiple metrics:</p>
    <ul>
      <li><strong>Workout Metrics:</strong> Completed workouts, calories burned, exercise minutes</li>
      <li><strong>Nutrition Metrics:</strong> Meals completed, calories consumed, macronutrients</li>
      <li><strong>Streaks:</strong> Current and longest activity streaks</li>
      <li><strong>Goals:</strong> Progress toward fitness objectives</li>
    </ul>

    <h3>User Scoring System</h3>
    <p>Comprehensive scoring system that motivates users:</p>
    <ul>
      <li><strong>Total Score:</strong> Overall user performance score</li>
      <li><strong>Component Scores:</strong> Workout, nutrition, streak, and goal scores</li>
      <li><strong>Achievements:</strong> Milestones and perfect weeks</li>
      <li><strong>Points System:</strong> Earned points for various activities</li>
    </ul>

    <div class="warning-box">
      <h4>⚠️ Important</h4>
      <p>Always verify user consent before modifying personal health information or deleting accounts.</p>
    </div>
  </div>

  <div id="workouts" class="doc-section">
    <h2>💪 Workout Management</h2>
    <p>Complete workout system management including plans, exercises, schedules, and user assignments.</p>

    <h3>Workout Plans</h3>
    <p>Create and manage comprehensive workout programs:</p>
    <ul>
      <li><strong>Plan Creation:</strong> Define workout structure, duration, and frequency</li>
      <li><strong>Targeting:</strong> Set fitness level, goals, location, and equipment requirements</li>
      <li><strong>Health Conditions:</strong> Specify which health conditions the plan accommodates</li>
      <li><strong>Demographics:</strong> Target specific age groups and genders</li>
      <li><strong>Media:</strong> Add cover images and promotional content</li>
    </ul>

    <h3>Workout Days</h3>
    <p>Design individual workout sessions:</p>
    <ul>
      <li><strong>Day Structure:</strong> Organize exercises into sections (warm-up, main, cool-down)</li>
      <li><strong>Exercise Selection:</strong> Add exercises with sets, reps, and timing</li>
      <li><strong>Rest Periods:</strong> Configure rest between exercises and sets</li>
      <li><strong>Progression:</strong> Set difficulty progression throughout the plan</li>
    </ul>

    <h3>Exercise Database</h3>
    <p>Comprehensive exercise library management:</p>
    <ul>
      <li><strong>Exercise Details:</strong> Name, description, muscle groups, difficulty</li>
      <li><strong>Media Assets:</strong> Images, videos, and GIFs for demonstration</li>
      <li><strong>Equipment:</strong> Required equipment and alternatives</li>
      <li><strong>Modifications:</strong> Easier and harder variations</li>
      <li><strong>Safety Notes:</strong> Important form cues and precautions</li>
    </ul>

    <h3>User Workout Plans</h3>
    <p>Manage individual user workout assignments:</p>
    <ul>
      <li><strong>Plan Assignment:</strong> Assign workout plans to specific users</li>
      <li><strong>Schedule Management:</strong> Set start dates and duration</li>
      <li><strong>Customization:</strong> Modify plans for individual user needs</li>
      <li><strong>Progress Tracking:</strong> Monitor completion and performance</li>
      <li><strong>Sync Options:</strong> Control how plan updates affect user schedules</li>
    </ul>

    <div class="success-box">
      <h4>✅ Best Practice</h4>
      <p>Always test workout plans thoroughly before assigning them to users. Ensure proper progression and safety.</p>
    </div>
  </div>

  <div id="meals" class="doc-section">
    <h2>🍽️ Meal Management</h2>
    <p>Comprehensive nutrition management system for meal plans, recipes, and dietary tracking.</p>

    <h3>Meal Plans</h3>
    <p>Create structured nutrition programs:</p>
    <ul>
      <li><strong>Plan Structure:</strong> Define daily calorie targets and meal frequency</li>
      <li><strong>Dietary Preferences:</strong> Support for keto, vegetarian, and other diets</li>
      <li><strong>Health Considerations:</strong> Accommodate allergies and medical conditions</li>
      <li><strong>Macronutrient Balance:</strong> Set protein, carb, and fat ratios</li>
      <li><strong>Hydration Goals:</strong> Include daily water intake targets</li>
    </ul>

    <h3>Individual Meals</h3>
    <p>Manage recipe database and meal details:</p>
    <ul>
      <li><strong>Nutritional Info:</strong> Calories, macronutrients, and micronutrients</li>
      <li><strong>Preparation Details:</strong> Prep time, cooking time, and difficulty</li>
      <li><strong>Ingredients:</strong> Complete ingredient lists with quantities</li>
      <li><strong>Instructions:</strong> Step-by-step cooking instructions</li>
      <li><strong>Media:</strong> Food photos and cooking videos</li>
      <li><strong>Categories:</strong> Meal type, cuisine, and dietary tags</li>
    </ul>

    <h3>Daily Meal Plans</h3>
    <p>Organize meals into daily schedules:</p>
    <ul>
      <li><strong>Meal Timing:</strong> Breakfast, lunch, dinner, and snacks</li>
      <li><strong>Portion Control:</strong> Serving sizes and calorie distribution</li>
      <li><strong>Variety:</strong> Ensure diverse nutrition and flavors</li>
      <li><strong>Preparation Flow:</strong> Optimize cooking and prep sequences</li>
    </ul>

    <h3>User Meal Plans</h3>
    <p>Personalized nutrition assignments:</p>
    <ul>
      <li><strong>Individual Customization:</strong> Adjust for personal preferences and restrictions</li>
      <li><strong>Progress Tracking:</strong> Monitor meal completion and nutrition intake</li>
      <li><strong>Substitutions:</strong> Allow ingredient and meal swaps</li>
      <li><strong>Shopping Lists:</strong> Generate grocery lists from meal plans</li>
    </ul>

    <div class="info-box">
      <h4>💡 Nutrition Tip</h4>
      <p>Always verify nutritional calculations and consider consulting with registered dietitians for specialized meal plans.</p>
    </div>
  </div>

  <div id="videos" class="doc-section">
    <h2>🎥 Video Management</h2>
    <p>Comprehensive video system for workout demonstrations, tutorials, and educational content.</p>

    <h3>Video Upload Process</h3>
    <ol class="step-list">
      <li><strong>Access Video Admin:</strong> Navigate to Workout Videos section in admin</li>
      <li><strong>Select Workout Plan:</strong> Choose which workout plan the video belongs to</li>
      <li><strong>Upload Video File:</strong> Select MP4 file (recommended format)</li>
      <li><strong>Add Thumbnail:</strong> Upload a preview image (JPG/PNG)</li>
      <li><strong>Set Details:</strong> Add title, description, and display order</li>
      <li><strong>Save and Preview:</strong> Test video playback and thumbnail display</li>
    </ol>

    <h3>Video Organization</h3>
    <ul>
      <li><strong>Workout Plan Association:</strong> Videos are linked to specific workout plans</li>
      <li><strong>Display Order:</strong> Control the sequence videos appear to users</li>
      <li><strong>Multiple Videos:</strong> Each workout plan can have multiple instructional videos</li>
      <li><strong>Duration Tracking:</strong> System automatically tracks video length</li>
    </ul>

    <h3>Video Features</h3>
    <ul>
      <li><strong>Preview in Admin:</strong> Watch videos directly in the admin interface</li>
      <li><strong>Thumbnail Generation:</strong> Custom thumbnails for better user experience</li>
      <li><strong>Mobile Optimization:</strong> Videos work across all devices</li>
      <li><strong>Playback Controls:</strong> Full video player with speed control and fullscreen</li>
    </ul>

    <h3>Video Best Practices</h3>
    <div class="feature-grid">
      <div class="feature-card">
        <h4>📹 Video Quality</h4>
        <ul>
          <li>Use 1080p resolution minimum</li>
          <li>Ensure good lighting and clear audio</li>
          <li>Keep videos focused and concise</li>
          <li>Include proper form demonstrations</li>
        </ul>
      </div>
      <div class="feature-card">
        <h4>🖼️ Thumbnails</h4>
        <ul>
          <li>Use high-quality, clear images</li>
          <li>Show the exercise being performed</li>
          <li>Maintain consistent style across videos</li>
          <li>Optimize for mobile viewing</li>
        </ul>
      </div>
    </div>

    <div class="warning-box">
      <h4>⚠️ File Size Considerations</h4>
      <p>Large video files may slow down the system. Compress videos while maintaining quality. Recommended: H.264 codec, MP4 format.</p>
    </div>
  </div>

  <div id="questionnaire" class="doc-section">
    <h2>❓ Questionnaire Management</h2>
    <p>Dynamic questionnaire system for collecting user information and personalizing plan assignments.</p>

    <h3>Question Types</h3>
    <ul>
      <li><strong>Text Input:</strong> Free-form text responses</li>
      <li><strong>Number Input:</strong> Numeric values (age, weight, etc.)</li>
      <li><strong>Single Choice:</strong> Radio button selections</li>
      <li><strong>Multiple Choice:</strong> Checkbox selections</li>
      <li><strong>Dropdown:</strong> Select from predefined options</li>
    </ul>

    <h3>Question Configuration</h3>
    <ul>
      <li><strong>Field Name:</strong> Internal identifier for data storage</li>
      <li><strong>Display Text:</strong> User-facing question text</li>
      <li><strong>Required Status:</strong> Mandatory vs. optional questions</li>
      <li><strong>Display Order:</strong> Question sequence in the form</li>
      <li><strong>Plan Assignment:</strong> Questions that affect workout/meal plan selection</li>
    </ul>

    <h3>Answer Options</h3>
    <p>For choice-based questions, manage answer options:</p>
    <ul>
      <li><strong>Option Text:</strong> Display text for users</li>
      <li><strong>Option Value:</strong> Internal value for processing</li>
      <li><strong>Option Order:</strong> Display sequence</li>
      <li><strong>Plan Mapping:</strong> Link options to specific plans</li>
    </ul>

    <h3>Adding New Questions</h3>
    <ol class="step-list">
      <li><strong>Access Questionnaire Manager:</strong> Navigate from admin dashboard</li>
      <li><strong>Click "Add Question":</strong> Start creating a new question</li>
      <li><strong>Set Question Details:</strong> Enter text, field name, and type</li>
      <li><strong>Configure Options:</strong> Add answer choices if applicable</li>
      <li><strong>Set Plan Assignment:</strong> Choose if question affects plan selection</li>
      <li><strong>Save and Test:</strong> Verify question appears correctly</li>
    </ol>

    <div class="info-box">
      <h4>💡 Question Design Tips</h4>
      <p>Keep questions clear and concise. Use simple language and avoid medical jargon. Test the questionnaire flow before deploying.</p>
    </div>
  </div>

  <div id="products" class="doc-section">
    <h2>🛒 Product Management</h2>
    <p>E-commerce integration for managing fitness products, supplements, and equipment.</p>

    <h3>Product Information</h3>
    <ul>
      <li><strong>Product Name:</strong> Clear, descriptive product titles</li>
      <li><strong>Description:</strong> Detailed product information and benefits</li>
      <li><strong>Images:</strong> High-quality product photos</li>
      <li><strong>Pricing:</strong> Current pricing and any discounts</li>
      <li><strong>Buy Links:</strong> External purchase URLs</li>
    </ul>

    <h3>Product Categories</h3>
    <ul>
      <li><strong>Supplements:</strong> Protein powders, vitamins, pre-workouts</li>
      <li><strong>Equipment:</strong> Home gym equipment, accessories</li>
      <li><strong>Apparel:</strong> Workout clothing and gear</li>
      <li><strong>Nutrition:</strong> Healthy food products and meal kits</li>
    </ul>

    <h3>Product Management Features</h3>
    <ul>
      <li><strong>Inventory Tracking:</strong> Monitor stock levels</li>
      <li><strong>Affiliate Links:</strong> Track referral commissions</li>
      <li><strong>Product Reviews:</strong> User feedback and ratings</li>
      <li><strong>Recommendations:</strong> Suggest products based on user plans</li>
    </ul>

    <div class="success-box">
      <h4>✅ Product Strategy</h4>
      <p>Focus on products that complement your workout and meal plans. Ensure all product links are current and functional.</p>
    </div>
  </div>

  <div id="support" class="doc-section">
    <h2>🎧 Support Management</h2>
    <p>Customer support system for handling user questions, feedback, and issues.</p>

    <h3>FAQ Management</h3>
    <ul>
      <li><strong>Question Categories:</strong> Organize FAQs by topic (workouts, nutrition, technical)</li>
      <li><strong>Answer Quality:</strong> Provide clear, helpful responses</li>
      <li><strong>Search Optimization:</strong> Use keywords users might search for</li>
      <li><strong>Regular Updates:</strong> Keep information current and accurate</li>
    </ul>

    <h3>User Feedback System</h3>
    <ul>
      <li><strong>Feedback Types:</strong> Bug reports, feature requests, general feedback</li>
      <li><strong>Priority Levels:</strong> Urgent, high, medium, low priority classification</li>
      <li><strong>Status Tracking:</strong> New, in progress, resolved, closed</li>
      <li><strong>Response Management:</strong> Track admin responses and resolution times</li>
    </ul>

    <h3>Feedback Resolution Process</h3>
    <ol class="step-list">
      <li><strong>Review Feedback:</strong> Read and understand the user's issue</li>
      <li><strong>Categorize:</strong> Assign appropriate type and priority</li>
      <li><strong>Investigate:</strong> Research the issue or request</li>
      <li><strong>Respond:</strong> Provide helpful response to user</li>
      <li><strong>Resolve:</strong> Mark as resolved when issue is fixed</li>
      <li><strong>Follow Up:</strong> Ensure user satisfaction</li>
    </ol>

    <div class="warning-box">
      <h4>⚠️ Response Time Goals</h4>
      <p>Aim to respond to urgent issues within 24 hours, and all feedback within 72 hours. Quick responses improve user satisfaction.</p>
    </div>
  </div>

  <div id="activity" class="doc-section">
    <h2>📊 Activity Logs</h2>
    <p>Comprehensive activity tracking system for monitoring user behavior and system usage.</p>

    <h3>Activity Types</h3>
    <ul>
      <li><strong>Workout Activities:</strong> Workout started, completed, session completed</li>
      <li><strong>Meal Activities:</strong> Meals completed, scheduled</li>
      <li><strong>User Activities:</strong> Login, logout, profile updates</li>
      <li><strong>System Activities:</strong> Plan assignments, data updates</li>
    </ul>

    <h3>Log Information</h3>
    <ul>
      <li><strong>User Identification:</strong> Which user performed the activity</li>
      <li><strong>Timestamp:</strong> Exact date and time of activity</li>
      <li><strong>Activity Description:</strong> Detailed description of what happened</li>
      <li><strong>Related Objects:</strong> Links to workouts, meals, or other relevant data</li>
      <li><strong>Metadata:</strong> Additional context and details</li>
    </ul>

    <h3>Using Activity Logs</h3>
    <ul>
      <li><strong>User Behavior Analysis:</strong> Understand how users interact with the platform</li>
      <li><strong>Troubleshooting:</strong> Investigate issues and errors</li>
      <li><strong>Performance Monitoring:</strong> Track system usage patterns</li>
      <li><strong>Compliance:</strong> Maintain audit trails for data changes</li>
    </ul>

    <div class="info-box">
      <h4>💡 Analytics Insights</h4>
      <p>Use activity logs to identify popular features, common user paths, and areas for improvement in the platform.</p>
    </div>
  </div>

  <div id="assignment" class="doc-section">
    <h2>📝 Plan Assignment</h2>
    <p>Intelligent system for assigning workout and meal plans to users based on their profiles and preferences.</p>

    <h3>Assignment Methods</h3>
    <ul>
      <li><strong>Manual Assignment:</strong> Directly assign specific plans to individual users</li>
      <li><strong>Bulk Assignment:</strong> Assign plans to multiple users at once</li>
      <li><strong>Automatic Assignment:</strong> System assigns plans based on questionnaire responses</li>
      <li><strong>Criteria-Based:</strong> Assign plans based on user characteristics</li>
    </ul>

    <h3>Assignment Criteria</h3>
    <ul>
      <li><strong>Fitness Level:</strong> Beginner, intermediate, advanced</li>
      <li><strong>Goals:</strong> Weight loss, muscle gain, endurance, strength</li>
      <li><strong>Location:</strong> Home, gym, outdoor workouts</li>
      <li><strong>Equipment:</strong> Available equipment and space</li>
      <li><strong>Health Conditions:</strong> Medical considerations and limitations</li>
      <li><strong>Demographics:</strong> Age group and gender preferences</li>
    </ul>

    <h3>Assignment Process</h3>
    <ol class="step-list">
      <li><strong>Access Assignment Tool:</strong> Navigate to "Assign Plans" from dashboard</li>
      <li><strong>Select Users:</strong> Choose individual users or user groups</li>
      <li><strong>Choose Plans:</strong> Select appropriate workout and meal plans</li>
      <li><strong>Set Schedule:</strong> Define start dates and duration</li>
      <li><strong>Review Assignment:</strong> Verify compatibility and settings</li>
      <li><strong>Execute Assignment:</strong> Apply plans to selected users</li>
    </ol>

    <h3>Sync Options</h3>
    <ul>
      <li><strong>No Sync:</strong> User plans remain independent of global plan changes</li>
      <li><strong>Sync Updates:</strong> User plans update when global plans are modified</li>
      <li><strong>Sync New Content:</strong> Only new content is synced to user plans</li>
    </ul>

    <div class="warning-box">
      <h4>⚠️ Assignment Considerations</h4>
      <p>Always verify plan compatibility with user health conditions and fitness levels before assignment. Consider user preferences and limitations.</p>
    </div>
  </div>

  <div id="calendar" class="doc-section">
    <h2>📅 Calendar Views</h2>
    <p>Visual calendar interfaces for managing workout and meal schedules across users and time periods.</p>

    <h3>Workout Calendar</h3>
    <ul>
      <li><strong>Monthly View:</strong> See workout schedules across entire months</li>
      <li><strong>User Selection:</strong> Filter by specific users or view all users</li>
      <li><strong>Plan Filtering:</strong> Focus on specific workout plans</li>
      <li><strong>Schedule Details:</strong> View workout days and rest days</li>
      <li><strong>Progress Tracking:</strong> See completed vs. scheduled workouts</li>
    </ul>

    <h3>Meal Calendar</h3>
    <ul>
      <li><strong>Daily Meal Plans:</strong> View complete daily nutrition schedules</li>
      <li><strong>Meal Timing:</strong> See breakfast, lunch, dinner, and snack timing</li>
      <li><strong>Nutritional Overview:</strong> Daily calorie and macro targets</li>
      <li><strong>Completion Status:</strong> Track meal completion rates</li>
    </ul>

    <h3>Calendar Features</h3>
    <ul>
      <li><strong>Navigation:</strong> Easy month-to-month navigation</li>
      <li><strong>Color Coding:</strong> Visual indicators for different plan types</li>
      <li><strong>Quick Actions:</strong> Direct links to edit schedules</li>
      <li><strong>Export Options:</strong> Generate printable schedules</li>
    </ul>

    <div class="success-box">
      <h4>✅ Calendar Benefits</h4>
      <p>Calendar views help identify scheduling conflicts, track user progress, and optimize plan timing for better user engagement.</p>
    </div>
  </div>

  <div id="security" class="doc-section">
    <h2>🔒 Security & Permissions</h2>
    <p>Comprehensive security system protecting user data and controlling admin access.</p>

    <h3>Admin Access Control</h3>
    <ul>
      <li><strong>Superuser Requirement:</strong> Only superusers can access the admin interface</li>
      <li><strong>Email Verification:</strong> Admin accounts require verified email addresses</li>
      <li><strong>Session Management:</strong> Automatic session timeout for security</li>
      <li><strong>Activity Logging:</strong> All admin actions are logged for audit trails</li>
    </ul>

    <h3>Data Protection</h3>
    <ul>
      <li><strong>User Privacy:</strong> Personal health information is protected</li>
      <li><strong>Data Encryption:</strong> Sensitive data is encrypted in storage</li>
      <li><strong>Access Logs:</strong> Track who accesses what data and when</li>
      <li><strong>Backup Security:</strong> Regular secure backups of all data</li>
    </ul>

    <h3>Permission Levels</h3>
    <ul>
      <li><strong>View Permissions:</strong> Read-only access to specific data</li>
      <li><strong>Edit Permissions:</strong> Modify existing records</li>
      <li><strong>Create Permissions:</strong> Add new records to the system</li>
      <li><strong>Delete Permissions:</strong> Remove records (with confirmation)</li>
    </ul>

    <h3>Security Best Practices</h3>
    <ol class="step-list">
      <li><strong>Strong Passwords:</strong> Use complex passwords for admin accounts</li>
      <li><strong>Regular Updates:</strong> Keep the system updated with security patches</li>
      <li><strong>Access Review:</strong> Regularly review who has admin access</li>
      <li><strong>Data Backup:</strong> Maintain regular, secure backups</li>
      <li><strong>Monitor Logs:</strong> Review activity logs for suspicious activity</li>
      <li><strong>User Training:</strong> Train all admins on security procedures</li>
    </ol>

    <div class="warning-box">
      <h4>⚠️ Security Alert</h4>
      <p>Never share admin credentials. Always log out when finished. Report any suspicious activity immediately.</p>
    </div>
  </div>

  <div class="doc-section" style="text-align: center; background: linear-gradient(135deg, #417690, #2c5282); color: white;">
    <h2>🎉 Congratulations!</h2>
    <p>You now have comprehensive knowledge of the AC-FIT admin system. Use this documentation as your reference guide for managing the platform effectively.</p>

    <div style="margin-top: 30px;">
      <h3>Quick Support</h3>
      <p>If you need additional help or have questions not covered in this documentation, please contact the development team.</p>
    </div>

    <div style="margin-top: 20px;">
      <a href="{% url 'acfit_admin:dashboard' %}" style="background: white; color: #417690; padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block; margin: 10px;">
        🏠 Return to Dashboard
      </a>
    </div>
  </div>
</div>

<a href="#" class="back-to-top">↑ Top</a>

<script>
// Smooth scrolling for navigation links
document.querySelectorAll('.doc-nav a').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Back to top functionality
document.querySelector('.back-to-top').addEventListener('click', function(e) {
    e.preventDefault();
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Show/hide back to top button based on scroll position
window.addEventListener('scroll', function() {
    const backToTop = document.querySelector('.back-to-top');
    if (window.pageYOffset > 300) {
        backToTop.style.display = 'block';
    } else {
        backToTop.style.display = 'none';
    }
});
</script>

{% endblock %}
