import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../widgets/custom_app_bar.dart';
import '../../services/api_config.dart';

class ProductImage {
  final String id;
  final String imageUrl;

  ProductImage({
    required this.id,
    required this.imageUrl,
  });

  factory ProductImage.fromJson(Map<String, dynamic> json) {
    return ProductImage(
      id: json['id'],
      imageUrl: json['image_url'],
    );
  }
}

class ProductDetail {
  final String id;
  final String name;
  final String slug;
  final String description;
  final String buyLink;
  final List<ProductImage> images;
  final String? primaryImage;

  ProductDetail({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.buyLink,
    required this.images,
    this.primaryImage,
  });

  factory ProductDetail.fromJson(Map<String, dynamic> json) {
    List<ProductImage> images = [];
    if (json['images'] != null) {
      images = (json['images'] as List)
          .map((imageJson) => ProductImage.fromJson(imageJson))
          .toList();
    }

    return ProductDetail(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      buyLink: json['buy_link'],
      images: images,
      primaryImage: json['primary_image'],
    );
  }
}

class ShopDetailScreen extends StatefulWidget {
  final String slug;

  const ShopDetailScreen({
    Key? key,
    required this.slug,
  }) : super(key: key);

  @override
  State<ShopDetailScreen> createState() => _ShopDetailScreenState();
}

class _ShopDetailScreenState extends State<ShopDetailScreen> {
  bool _isLoading = true;
  bool _hasError = false;
  ProductDetail? _product;
  int _currentImageIndex = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _loadProductDetails();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // Method to launch URLs with better error handling
  Future<void> _launchURL(String urlString) async {
    try {
      // Clean up the URL string
      final String cleanUrl = urlString.trim();
      print('Attempting to launch URL: $cleanUrl');

      // Create a Uri object
      final Uri url = Uri.parse(cleanUrl);

      // Try to launch the URL in an external application
      final bool canLaunch = await canLaunchUrl(url);
      print('Can launch URL: $canLaunch');

      if (canLaunch) {
        final bool launched = await launchUrl(
          url,
          mode: LaunchMode.externalApplication,
        );
        print('URL launch result: $launched');

        if (!launched && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Could not open the link: $cleanUrl'),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        // Fallback to a web intent for Android
        print('Cannot launch URL directly, trying alternative method');

        // Show a snackbar to inform the user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Opening link in browser: $cleanUrl'),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      print('Error launching URL: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening the link: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _loadProductDetails() async {
    // Check if the widget is still mounted before calling setState
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      print(
          'ShopDetailScreen: Loading product details for slug: ${widget.slug}');

      final response = await http.get(
        Uri.parse(ApiConfig.getProductDetailUrl(widget.slug)),
      );

      print('ShopDetailScreen: Response status code: ${response.statusCode}');

      // Check if the widget is still mounted before continuing
      if (!mounted) return;

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('ShopDetailScreen: Product data loaded successfully');

        // Check if the widget is still mounted before calling setState
        if (!mounted) return;

        final product = ProductDetail.fromJson(data);

        // Debug logging for images
        print('Product has ${product.images.length} images');
        if (product.images.isNotEmpty) {
          print('First image URL: ${product.images[0].imageUrl}');
        }
        if (product.primaryImage != null) {
          print('Primary image URL: ${product.primaryImage}');
        }

        setState(() {
          _product = product;
          _isLoading = false;
        });
      } else {
        print(
            'ShopDetailScreen: Error loading product details - Status code: ${response.statusCode}');

        // Check if the widget is still mounted before calling setState
        if (!mounted) return;

        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    } catch (e) {
      print('ShopDetailScreen: Exception while loading product details: $e');

      // Check if the widget is still mounted before calling setState
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: _product?.name ?? 'Product Details',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.black),
            )
          : _hasError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 70,
                        color: Colors.black,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Failed to load product details',
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton.icon(
                        onPressed: _loadProductDetails,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 30,
                            vertical: 15,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : _product == null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.shopping_bag_outlined,
                            size: 70,
                            color: Colors.black38,
                          ),
                          const SizedBox(height: 20),
                          Text(
                            'Product not found',
                            style: theme.textTheme.titleLarge?.copyWith(
                              color: Colors.black54,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Stack(
                      children: [
                        // Main content
                        SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Image Carousel
                              _buildImageCarousel(),

                              // Product Details
                              Container(
                                padding: const EdgeInsets.all(24),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(30),
                                    topRight: Radius.circular(30),
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(30),
                                      blurRadius: 15,
                                      offset: const Offset(0, -8),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Product Name
                                    Text(
                                      _product!.name,
                                      style: theme.textTheme.headlineSmall
                                          ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                        letterSpacing: 0.5,
                                      ),
                                    ),

                                    const SizedBox(height: 24),

                                    // Description Section
                                    Text(
                                      'DESCRIPTION',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                        letterSpacing: 1.5,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    Text(
                                      _product!.description,
                                      style:
                                          theme.textTheme.bodyLarge?.copyWith(
                                        color: Colors.black87,
                                        height: 1.6,
                                      ),
                                    ),

                                    const SizedBox(height: 32),

                                    // Product Images Section (if available)
                                    if (_product!.images.isNotEmpty)
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'PRODUCT GALLERY',
                                            style: theme.textTheme.titleMedium
                                                ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black87,
                                              letterSpacing: 1.5,
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          SizedBox(
                                            height: 100,
                                            child: ListView.builder(
                                              scrollDirection: Axis.horizontal,
                                              itemCount:
                                                  _product!.images.length,
                                              itemBuilder: (context, index) {
                                                return Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 16),
                                                  child: GestureDetector(
                                                    onTap: () {
                                                      _pageController
                                                          .animateToPage(
                                                        index,
                                                        duration:
                                                            const Duration(
                                                                milliseconds:
                                                                    300),
                                                        curve: Curves.easeInOut,
                                                      );
                                                    },
                                                    child: Container(
                                                      width: 100,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        border: Border.all(
                                                          color:
                                                              _currentImageIndex ==
                                                                      index
                                                                  ? Colors.black
                                                                  : Colors.grey
                                                                      .shade300,
                                                          width: 2,
                                                        ),
                                                      ),
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                        child:
                                                            CachedNetworkImage(
                                                          imageUrl: _product!
                                                              .images[index]
                                                              .imageUrl,
                                                          fit: BoxFit.cover,
                                                          placeholder:
                                                              (context, url) =>
                                                                  Container(
                                                            color: Colors
                                                                .grey[200],
                                                            child: const Center(
                                                              child:
                                                                  CircularProgressIndicator(
                                                                color: Colors
                                                                    .black,
                                                                strokeWidth: 2,
                                                              ),
                                                            ),
                                                          ),
                                                          errorWidget: (context,
                                                                  url, error) =>
                                                              Container(
                                                            color: Colors
                                                                .grey[200],
                                                            child: const Icon(
                                                              Icons
                                                                  .image_not_supported,
                                                              size: 30,
                                                              color: Colors
                                                                  .black38,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),

                                    const SizedBox(
                                        height: 100), // Space for the button
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Fixed Buy Now button at the bottom
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(20),
                                  blurRadius: 15,
                                  offset: const Offset(0, -5),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed: () {
                                _launchURL(_product!.buyLink);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 18),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 0,
                              ),
                              child: const Text(
                                'BUY NOW',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1.5,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
    );
  }

  Widget _buildImageCarousel() {
    // If there are no images, show a placeholder
    if (_product!.images.isEmpty) {
      return Container(
        height: 300,
        width: double.infinity,
        color: Colors.grey[200],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_not_supported,
                size: 60,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                'No images available',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Get image URLs from the product
    final List<String> imageUrls =
        _product!.images.map((img) => img.imageUrl).toList();

    // If there's a primary image and it's not in the list, add it
    if (_product!.primaryImage != null &&
        !imageUrls.contains(_product!.primaryImage)) {
      imageUrls.insert(0, _product!.primaryImage!);
    }

    return Stack(
      children: [
        // Image Carousel
        SizedBox(
          height: 350,
          width: double.infinity,
          child: PageView.builder(
            controller: _pageController,
            itemCount: imageUrls.length,
            onPageChanged: (index) {
              if (mounted) {
                setState(() {
                  _currentImageIndex = index;
                });
              }
            },
            itemBuilder: (context, index) {
              return Hero(
                tag: 'product_image_${_product!.id}_$index',
                child: CachedNetworkImage(
                  imageUrl: imageUrls[index],
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[100],
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFFF97316),
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: 60,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // Gradient overlay at the bottom for better visibility
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withAlpha(120),
                ],
              ),
            ),
          ),
        ),

        // Image Indicators
        if (imageUrls.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                imageUrls.length,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: _currentImageIndex == index ? 16 : 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: _currentImageIndex == index
                        ? Colors.white
                        : Colors.white.withAlpha(153),
                  ),
                ),
              ),
            ),
          ),

        // Navigation arrows
        if (imageUrls.length > 1) ...[
          // Left arrow
          Positioned(
            left: 16,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () {
                if (_currentImageIndex > 0) {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(77),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),

          // Right arrow
          Positioned(
            right: 16,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onTap: () {
                if (_currentImageIndex < imageUrls.length - 1) {
                  _pageController.nextPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(77),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
